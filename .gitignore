# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

#Workflow
.github/

# System files
.DS_Store
Thumbs.db

# Diagnostic reports
report.[0-9]*.[0-9]*.[0-9]*.[0-9]*.json

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Instrumented libs
lib-cov

# Coverage
coverage/
*.lcov
.nyc_output/

# Build artifacts
build/
dist/
.release/
public/

# Dependency directories
node_modules/
jspm_packages/

# Binary addons
build/Release

# Caches
.cache/
.parcel-cache/
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/
.eslintcache
.stylelintcache
.vitepress/cache/
.yarn/cache/
.yarn/unplugged/
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# REPL & temp files
.node_repl_history
*.tsbuildinfo

# Package archives
*.tgz

# dotenv config files
.env
.env.local
.env.*.local

# IDE / editor files
.vscode/
.idea/
*.swp

# Sequelize CLI files (if generated)
sequelize-data/
sequelize-cli/

# MySQL temp dump or socket files (if applicable)
*.sql
*.sql.gz
*.mysql
*.sock

# Elasticsearch data (in local dev only, don't sync cluster data)
elasticsearch-data/

# Redis (if using with Fastify)
dump.rdb
appendonly.aof

# Docker
docker-compose.override.yml
*.pid
*.sock

# Misc
bower_components/
.lock-wscript
.serverless/
.tern-port
.dynamodb/
.vscode-test

# Ignore test output
test-output/
/src/utils/category.js