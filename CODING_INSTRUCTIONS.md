# Shopee Project - Coding Instructions

## Response Handling Standards

### Success Response Format
Always use this standardized format for success responses in controllers:

```javascript
return successResponse(
    reply,
    statusCode,
    message,
    status,
    data
);
```

**Parameters:**
- `reply` - Fastify reply object (always use `reply`, not `res`)
- `statusCode` - HTTP status code (200, 201, etc.)
- `message` - Success message string
- `status` - Status string ("success" or "failure")
- `data` - Response data object/array

### Error Response Format
Always use this standardized format for error responses in controllers:

```javascript
return errorResponse(reply, statusCode, error, data, status, request);
```

**Parameters:**
- `reply` - Fastify reply object (always use `reply`, not `res`)
- `statusCode` - HTTP error status code (400, 500, etc.)
- `error` - Error object (not error.message, pass the full error)
- `data` - Data object (use `{}` for empty object, not `[]`)
- `status` - Status string (always "error")
- `request` - Request object for context

### Controller Function Parameters
- Always use `(req, reply)` as parameter names
- Never use `(req, res)` - this causes "reply is not defined" errors
- `req` = request object, `reply` = Fastify reply object

### Example Implementation
```javascript
export const myController = async (req, reply) => {
    try {
        const result = await myService(req);
        return successResponse(
            reply,
            result.statusCode,
            result.message,
            result.status,
            result.data
        );
    } catch (err) {
        return errorResponse(reply, 500, err, {}, "error", req);
    }
};
```

## General Guidelines
- This is a Node.js/Sequelize backend project with a modular structure.
- All models are defined using Sequelize and are located in `src/models/`.
- Services are in `src/services/` and handle business logic, often using models and utility functions.
- Route definitions are in `src/routes/`, and controllers are in `src/controllers/`.
- Configuration and region-specific data are in `src/region/config.js`.
- Use ES6 module syntax throughout the codebase.

## Models
- Models are defined using `sequelize.define` and exported as default.
- Some models (e.g., subcategory, subsubcategory) use factory functions to allow dynamic table names based on country.
- Associations are set up via an `associate` method on the model, called in `src/models/index.js`.
- Always check for and use the `associate` method when adding new models.

## Services
- Services import models from `src/models/index.js` or directly from model files.
- Business logic, data fetching, and transformation are handled here.
- Use utility functions from `src/services/common.service.js` for data formatting and transformation.
- When adding new service functions, follow the async/await pattern and handle errors with try/catch.

## Controllers
- Controllers are responsible for handling Fastify requests and responses.
- They import service functions and pass request data to them.
- Always validate and sanitize input in controllers before passing to services.
- **ALWAYS use the standardized response formats above**

## Routes
- Route files export a function that registers endpoints on the Fastify instance.
- Route handlers are controller functions imported from `src/controllers/`.
- Use RESTful naming conventions for endpoints.

## Utilities
- Utility functions (e.g., data transformation, formatting) are in `src/services/common.service.js`.
- Use these utilities for consistent data formatting across services and controllers.

## Configuration
- Region and currency configuration is in `src/region/config.js`.
- Use these configs for country-specific logic, currency, and category mappings.

## Error Handling
- Always use try/catch in async functions in services and controllers.
- Return meaningful error messages and HTTP status codes.
- **Follow the standardized error response format above**

## Adding New Features
- Add new models in `src/models/` and update `src/models/index.js` for associations.
- Add business logic in `src/services/` and expose via controllers and routes.
- Use existing patterns for error handling, data transformation, and response formatting.

## Testing & Debugging
- Use logging and error messages for debugging.
- Check model associations and table names carefully, especially for region-specific models.

---

*These instructions are auto-generated by Copilot for future reference and onboarding. Update as the codebase evolves.*
