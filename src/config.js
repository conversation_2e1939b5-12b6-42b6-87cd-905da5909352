import fp from "fastify-plugin";
import { Client } from "@elastic/elasticsearch";
import http from "http"; // Required for keepAliveAgent

let clientInstance = null;
let initializingPromise = null;
let keepAliveAgent = null;

/**
 * Singleton Elasticsearch initializer with keep-alive agent and retry logic.
 */
async function getElasticsearchClient(maxRetries = 3, delay = 3000) {
    if (clientInstance) return clientInstance;

    if (!initializingPromise) {
        initializingPromise = (async () => {
            if (!keepAliveAgent) {
                keepAliveAgent = new http.Agent({
                    keepAlive: true,
                    maxFreeSockets: 100,
                    keepAliveMsecs: 10000,
                    timeout: 60000,
                });
            }

            const client = new Client({
                node: process.env.ELASTIC_HOST,
                auth: {
                    username: process.env.ELASTIC_USER,
                    password: process.env.ELASTIC_PASSWORD,
                },
                transport: {
                    agent: () => keepAliveAgent,
                },
                maxRetries: 3,
                requestTimeout: 15000,
                sniffOnStart: false,
            });

            for (let attempt = 1; attempt <= maxRetries; attempt++) {
                try {
                    await client.ping();
                    console.info(
                        `Elasticsearch connected (attempt ${attempt})`
                    );
                    clientInstance = client;
                    return client;
                } catch (err) {
                    console.error(
                        `Attempt ${attempt} - Elasticsearch ping failed: ${err.message}`
                    );
                    if (attempt < maxRetries) {
                        await new Promise((res) => setTimeout(res, delay));
                    } else {
                        throw new Error(
                            "Elasticsearch connection failed after max retries"
                        );
                    }
                }
            }
        })();
    }

    return initializingPromise;
}

/**
 * Fastify plugin for registering a singleton Elasticsearch client.
 */
async function elasticsearchPlugin(fastify, opts) {
    try {
        const client = await getElasticsearchClient();
        fastify.decorate("elasticsearch", client);
    } catch (err) {
        console.error("Elasticsearch plugin failed to initialize:", err);
        throw err; // Prevent Fastify from starting if ES is critical
    }
}

export default fp(elasticsearchPlugin, {
    name: "elasticsearchPlugin",
});
