import { successResponse, errorResponse } from "../utils/response.js";
import {
    getAllAddressesService,
    addAndUpdateAddressService,
    saveDefaultAddressService,
    deleteUserAddressService,
    selectAddressService,
    changeAddressStatusService,
} from "../services/address.service.js";

export const getAllAddresses = async (request, reply) => {
    try {
        const result = await getAllAddressesService(request);

        return successResponse(
            reply,
            result.statusCode,
            result.message,
            result.status,
            result.result
        );
    } catch (err) {
        return errorResponse(reply, 500, err, [], "error", request);
    }
};

export const addAndUpdateAddress = async (request, reply) => {
    try {
        const result = await addAndUpdateAddressService(request);

        return successResponse(
            reply,
            result.statusCode,
            result.message,
            result.status,
            result.result
        );
    } catch (err) {
        return errorResponse(reply, 500, err, [], "error", request);
    }
};

export const saveDefaultAddress = async (request, reply) => {
    try {
        const result = await saveDefaultAddressService(request);

        return successResponse(
            reply,
            result.statusCode,
            result.message,
            result.status,
            result.result
        );
    } catch (err) {
        return errorResponse(reply, 500, err, [], "error", request);
    }
};

export const deleteUserAddress = async (request, reply) => {
    try {
        const result = await deleteUserAddressService(request);
        return successResponse(
            reply,
            result.statusCode,
            result.message,
            result.status,
            result.result
        );
    } catch (err) {
        return errorResponse(reply, 500, err, [], "error", request);
    }
};

export const selectAddress = async (request, reply) => {
    try {
        const result = await selectAddressService(request);

        return successResponse(
            reply,
            result.statusCode,
            result.message,
            result.status,
            result.result
        );
    } catch (err) {
        return errorResponse(reply, 500, err, [], "error", request);
    }
};

export const changeAddressStatus = async (request, reply) => {
    try {
        const result = await changeAddressStatusService(request);

        return successResponse(
            reply,
            result.statusCode,
            result.message,
            result.status,
            result.result
        );
    } catch (err) {
        return errorResponse(reply, 500, err, [], "error", request);
    }
};
