import { successResponse, errorResponse } from "./../utils/response.js";
import {
    checkEmailExistence,
    checkMobileOTP,
    verifyOtpRecord,
    deleteOtpById,
    registerUser,
    authenticateUser,
    verifyResetToken,
    // updatePassword,
} from "../services/auth.service.js";

import jwt from "jsonwebtoken";

import { sendEmailUsingMailGen } from "../utils/email.js";
import CrmCustomer from "../models/crm_customer.model.js";
import { COUNTRY_NAMES } from "../region/config.js";

export const isEmailExist = async (request, reply) => {
    try {
        const result = await checkEmailExistence(request.body.email, reply);
        console.log("result", result);
        if (result) {
            return successResponse(
                reply,
                200,
                "This email is already registered with us...Please login to continue",
                "error",
                []
            );
        } else {
            return successResponse(
                reply,
                200,
                "Email not found",
                "success",
                []
            );
        }
    } catch (err) {
        return errorResponse(reply, 500, err, [], "error", request);
    }
};

export const checkMobile = async (request, reply) => {
    try {
        const result = await checkMobileOTP(request, "mobile");

        return successResponse(
            reply,
            result.statusCode,
            result.message,
            result.success,
            result.data
        );
    } catch (err) {
        return errorResponse(reply, 500, err, [], "error", request);
    }
};

export const reSendOtp = async (request, reply) => {
    try {
        const result = await checkMobileOTP(request, "mobile");

        return successResponse(
            reply,
            result.statusCode,
            result.message,
            result.success,
            result.data
        );
    } catch (err) {
        return errorResponse(reply, 500, err, [], "error");
    }
};

export const reSendOtpWhatsapp = async (request, reply) => {
    try {
        const result = await checkMobileOTP(request, "whatsup");

        return successResponse(
            reply,
            result.statusCode,
            result.message,
            result.success,
            result.data
        );
    } catch (err) {
        return errorResponse(reply, 500, err, [], "error");
    }
};

export const verifyOTP = async (request, reply) => {
    try {
        const result = await verifyOtpRecord(
            request.body.mobile,
            request.body.otp
        );
        if (result?.id) {
            const deleted = await deleteOtpById(result.id);
            if (deleted)
                return successResponse(
                    reply,
                    200,
                    "OTP verified successfully",
                    "success",
                    []
                );
        }

        return successResponse(
            reply,
            200,
            "Please enter valide OTP",
            "invalid",
            []
        );
    } catch (err) {
        return errorResponse(reply, 500, err, [], "error", request);
    }
};

export const signUp = async (request, reply) => {
    try {
        const result = await registerUser(request.body);

        return successResponse(
            reply,
            result.statusCode,
            result.message,
            result.success,
            result.data
        );
    } catch (err) {
        return errorResponse(reply, 500, err, [], "error", request);
    }
};

export const signIn = async (request, reply) => {
    try {
        const result = await authenticateUser(
            request.body.email,
            request.body.password,
            request.body.oscad
        );

        return successResponse(
            reply,
            result.statusCode,
            result.message,
            result.success,
            result.data
        );
    } catch (err) {
        return errorResponse(reply, 500, err, [], "error", request);
    }
};

export const forgotPassword = async (request, reply) => {
    try {
        const result = await checkEmailExistence(request.body.email);

        if (!result) {
            return successResponse(
                reply,
                200,
                "Email not found",
                "invalid",
                []
            );
        }

        const token = jwt.sign(
            { email: request.body.email },
            process.env.TOKEN_SECRET,
            {
                expiresIn: "10m",
            }
        );
        const baseURL = `${COUNTRY_NAMES[request.country_id]}_BASE_URL`;
        const resetLink = `${process.env[baseURL]}/reset-password/${token}`;

        await sendEmailUsingMailGen({
            email: result?.email,
            // email: '<EMAIL>',
            subject: "Password reset request",
            username: `${result.first_name || "Dear"}`,
            // ! NOTE: Following link should be the link of the frontend page responsible to request password reset
            // ! Frontend will send the below token with the new password in the request body to the backend reset password endpoint,
            resetlink: `${resetLink}`,
        });

        return successResponse(
            reply,
            200,
            "Password reset link sent successfully sent to your email",
            "success"
        );
    } catch (err) {
        return errorResponse(reply, 500, err, [], "error", request);
    }
};

export const verifyForgotPasswordToken = async (request, reply) => {
    try {
        const result = await verifyResetToken(request.params["*"]);

        return successResponse(
            reply,
            result.statusCode,
            result.message,
            result.success,
            result.data
        );
    } catch (err) {
        return errorResponse(reply, 500, err, [], "error", request);
    }
};

export const resetPassword = async (request, reply) => {
    try {
        const result = await verifyResetToken(request.params["*"]);

        if (result.success == "error") {
            return successResponse(
                reply,
                result.statusCode,
                result.message,
                result.success,
                result.data
            );
        }
        const { password, confirm_password, email } = request.body;

        if (password !== confirm_password) {
            return successResponse(
                reply,
                200,
                "Passwords does't match",
                "error",
                []
            );
        }

        const user = await CrmCustomer.findOne({
            where: { email: result.data.email },
        });

        if (!user) {
            return successResponse(reply, 200, "Email not found!", "error", []);
        }

        user.password = password;
        // user.changed('password', true);

        if (await user.save()) {
            return successResponse(
                reply,
                200,
                "Password updated successfully",
                "success",
                []
            );
        } else {
            return successResponse(
                reply,
                200,
                "Something went wrong! While processing your request",
                "error",
                []
            );
        }
    } catch (err) {
        return errorResponse(reply, 500, err, [], "error", request);
    }
};

export const sendEnquiryMails = async (request, reply) => {
    try {
        const { firstName, lastName, email, phoneNumber, message } =
            request.body;

        const htmlContent = `
            <h3>New Contact Form Submission</h3>
            <p><strong>Name:</strong> ${firstName} ${lastName}</p>
            <p><strong>Email:</strong> ${email}</p>
            <p><strong>Phone:</strong> ${phoneNumber}</p>
            <p><strong>Message:</strong></p>
            <p>${message}</p>
        `;

        await sendEmailUsingMailGen({
            email: "<EMAIL>",
            subject: `Enquiry from Vendor ${firstName} ${lastName}`,
            htmlContent,
        });

        return successResponse(
            reply,
            200,
            `Mail sent successfully to ${email}`,
            "success"
        );
    } catch (err) {
        console.log("error from sendEnquiryMails in auth controller", err);
        return errorResponse(reply, 500, err, [], "error", request);
    }
};
