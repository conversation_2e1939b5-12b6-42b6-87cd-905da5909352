import { getAllBrandItemsService } from "../services/brand.service.js";

import { asyncHandler } from "../utils/asyncHandler.js";
import { errorResponse, successResponse } from "../utils/response.js";

export const getAllBrandItems = asyncHandler(async (request, reply) => {
    try {
        const data = await getAllBrandItemsService(request, request.server);
        successResponse(
            reply,
            200,
            "Data fetched successfully",
            "success",
            data
        );
    } catch (err) {
        console.log(err);
        errorResponse(reply, 500, err);
    }
});
