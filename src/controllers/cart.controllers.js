import { successResponse, errorResponse } from "./../utils/response.js";

import {
    AddToCart,
    GetFromCart,
    directBuy,
    changeCartQuantity,
    removeFromCart,
    updateCartStatus,
    getUserDefaultAddress,
    GetPlaceOrder,
    GetDirectPlaceOrder,
    postPlaceOrder,
    checkCouponCode,
} from "../services/cart.service.js";

export const addToCart = async (request, reply) => {
    try {
        const data = await AddToCart(request);
        return successResponse(
            reply,
            200,
            "Data Saved Successfully",
            "success",
            data
        );
    } catch (err) {
        console.log("error from AddToCart controller", err);
        return errorResponse(reply, 500, err, [], "error");
    }
};

export const getFromCart = async (request, reply) => {
    try {
        const data = await GetFromCart(request);
        // console.log(data);
        if (data === "notfound") return errorResponse(reply, 500, [], "error");

        return successResponse(
            reply,
            200,
            "Cart fetched sucessfully",
            "success",
            data
        );
    } catch (err) {
        console.log("error from GetFromCart controller", err);
        return errorResponse(reply, 500, err, [], "error");
    }
};

export const ChangeCartQuantity = async (request, reply) => {
    try {
        const data = await changeCartQuantity(request);
        if (data === "error") return errorResponse(reply, 500, [], "error");

        return successResponse(
            reply,
            200,
            "Data Saved Successfully",
            "success",
            data
        );
    } catch (err) {
        console.log("error from changeCartQuantity controller", err);
        return errorResponse(reply, 500, err, [], "error");
    }
};

export const getPlaceOrder = async (request, reply) => {
    try {
        const data = await GetPlaceOrder(request);
        if (data === "notfound") return errorResponse(reply, 500, [], "error");

        return successResponse(
            reply,
            200,
            "Data Saved Successfully",
            "success",
            data
        );
    } catch (err) {
        console.log("error from GetPlaceOrder controller", err);
        return errorResponse(reply, 500, err, [], "error");
    }
};

export const RemoveFromCart = async (request, reply) => {
    try {
        const data = await removeFromCart(request);
        if (data === "error") return errorResponse(reply, 500, [], "error");

        return successResponse(
            reply,
            200,
            "Data Saved Successfully",
            "success",
            data
        );
    } catch (err) {
        console.log("error from removeFromCart controller", err);
        return errorResponse(reply, 500, err, [], "error");
    }
};

export const PostPlaceOrder = async (request, reply) => {
    try {
        const data = await postPlaceOrder(request);
        return successResponse(
            reply,
            200,
            "Data Saved Successfully",
            "success",
            data
        );
    } catch (err) {
        console.log("error from postPlaceOrder controller", err);
        return errorResponse(reply, 500, err, [], "error");
    }
};

export const DirectBuy = async (request, reply) => {
    try {
        const data = await directBuy(request);
        return successResponse(
            reply,
            200,
            "Data Saved Successfully",
            "success",
            data
        );
    } catch (err) {
        console.log("error from directBuy controller", err);
        return errorResponse(reply, 500, err, [], "error");
    }
};

export const getDirectPlaceOrder = async (request, reply) => {
    try {
        const data = await GetDirectPlaceOrder(request);
        return successResponse(
            reply,
            200,
            "Data Saved Successfully",
            "success",
            data
        );
    } catch (err) {
        console.log("error from GetDirectPlaceOrder controller", err);
        return errorResponse(reply, 500, err, [], "error");
    }
};

export const UpdateCartStatus = async (request, reply) => {
    try {
        const data = await updateCartStatus(request);
        if (data === "error") return errorResponse(reply, 500, [], "error");

        return successResponse(
            reply,
            200,
            "Data Saved Successfully",
            "success",
            data
        );
    } catch (err) {
        console.log("error from updateCartStatus controller", err);
        return errorResponse(reply, 500, err, [], "error");
    }
};

export const GetUserDefaultAddress = async (request, reply) => {
    try {
        const data = await getUserDefaultAddress(request);
        return successResponse(
            reply,
            200,
            "Data Saved Successfully",
            "success",
            data
        );
    } catch (err) {
        console.log("error from getUserDefaultAddress controller", err);
        return errorResponse(reply, 500, err, [], "error");
    }
};

export const validateCoupon = async (request, reply) => {
    try {
        // console.log("validateCoupon called with body:", request.body);
        const { coupon, tamount } = request.body;

        // Check for missing or empty coupon and tamount
        if (
            !coupon ||
            coupon.trim() === "" ||
            !tamount ||
            tamount === "" ||
            isNaN(parseFloat(tamount))
        ) {
            console.log("Validation failed - missing required fields");
            return errorResponse(
                reply,
                400,
                "Missing or invalid required fields: coupon and tamount",
                {},
                "error",
                request
            );
        }

        const result = await checkCouponCode(request);
        const status = "success";
        const statusCode = 200;
        const msg = result.sucess ? "" : result.msg;

        return successResponse(
            reply,
            statusCode,
            result.msg || "Coupon validated successfully",
            status,
            {
                msg,
                discount: result.discount || "",
                coupon_code: coupon,
                total_amount: parseFloat(tamount),
            }
        );
    } catch (error) {
        return errorResponse(reply, 500, error, {}, "error", request);
    }
};
