import {
    getAllCategoryListService,
    getAllCategoryItemsService,
    getAllItemsService,
    subcategoryMetatagsService,
    subsubcategoryMetatagsService,
    brandsMetatagsService,
} from "../services/category.service.js";
import { getAllCategoriesElk } from "../elk/category.elk.js";
import { asyncHandler } from "../utils/asyncHandler.js";
import { successResponse, errorResponse } from "../utils/response.js";

export const getCategoryList = asyncHandler(async (request, reply) => {
    try {
        const data = await getAllCategoryListService(request, request.server);
        successResponse(
            reply,
            200,
            "Data fetched successfully",
            "success",
            data
        );
    } catch (err) {
        console.error(err);
        errorResponse(reply, 500, err);
    }
});

export const getAllCategoryItems = asyncHandler(async (request, reply) => {
    try {
        if (!request.query.cat_url) {
            throw new Error("cat_url is required!");
        }
        const data = await getAllCategoryItemsService(request, request.server);
        successResponse(
            reply,
            200,
            "Data fetched successfully",
            "success",
            data
        );
    } catch (err) {
        // console.error(err);
        errorResponse(reply, 500, err);
    }
});

export const getAllItems = asyncHandler(async (request, reply) => {
    try {
        const data = await getAllItemsService(request, request.server);
        successResponse(
            reply,
            200,
            "Data fetched successfully",
            "success",
            data
        );
    } catch (err) {
        console.error(err);
        errorResponse(reply, 500, err);
    }
});

export const subcategoryMetatags = asyncHandler(async (request, reply) => {
    try {
        if (!request.body.url) {
            throw new Error("url is required!");
        }
        const data = await subcategoryMetatagsService(request);
        successResponse(
            reply,
            200,
            "Data fetched successfully",
            "success",
            data
        );
    } catch (err) {
        console.error(err);
        errorResponse(reply, 500, err);
    }
});

export const subsubcategoryMetatags = asyncHandler(async (request, reply) => {
    try {
        if (!request.body.url) {
            throw new Error("url is required!");
        }
        const data = await subsubcategoryMetatagsService(request);
        successResponse(
            reply,
            200,
            "Data fetched successfully",
            "success",
            data
        );
    } catch (err) {
        console.error(err);
        errorResponse(reply, 500, err);
    }
});

export const brandsMetatags = asyncHandler(async (request, reply) => {
    try {
        if (!request.body.url) {
            throw new Error("url is required!");
        }
        const data = await brandsMetatagsService(request, request.server);
        successResponse(
            reply,
            200,
            "Data fetched successfully",
            "success",
            data
        );
    } catch (err) {
        console.error(err);
        errorResponse(reply, 500, err);
    }
});

export const getAllCategories = async (request, reply) => {
    try {
        // You can get countryId from header or query, default to 1
        // const countryId = request.headers["country-id"] || 1;
        // Optional: support query params for filtering
        const { size, from, categoryLevel, parentId, frontView, status } =
            request.query;

        const categories = await getAllCategoriesElk(request.server, {
            size: size ? Number(size) : undefined,
            from: from ? Number(from) : undefined,
            categoryLevel: categoryLevel ? Number(categoryLevel) : undefined,
            parentId: parentId ? Number(parentId) : undefined,
            frontView: frontView ? Number(frontView) : undefined,
            status: status ? Number(status) : undefined,
        });

        return reply.code(200).send({
            status: "success",
            message: "Categories fetched successfully",
            data: categories,
        });
    } catch (error) {
        throw error;
    }
};
