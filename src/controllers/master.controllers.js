import { successResponse } from "./../utils/response.js";
import {
  GetNationalityService,
  getLocationsService,
  getAreasService,
} from "../services/master.service.js";

export const GetNationality = async (request, reply) => {
  try {
    const result = await GetNationalityService();

    return successResponse(
      reply,
      result.statusCode,
      result.message,
      result.status,
      result.data
    );
  } catch (err) {
    return errorResponse(reply, 500, err, [], "error");
  }
};

export const getLocations = async (request, reply) => {
  try {
    const result = await getLocationsService(request.country_id, request.query);
    console.log(result.statusCode, result.message, result.status, result.data);
    return successResponse(
      reply,
      result.statusCode,
      result.message,
      result.status,
      result.data
    );
  } catch (err) {
    return errorResponse(reply, 500, err, [], "error");
  }
};

export const getAreas = async (request, reply) => {
  try {
    const result = await getAreasService(request.country_id, request.query);

    return reply.code(result.statusCode).send({
      status: result.status,
      message: result.message,
      data: result.data,
    });
  } catch (err) {
    return errorResponse(reply, 500, err, [], "error");
  }
};
