import { successResponse, errorResponse } from "./../utils/response.js";

import {
    handleTabbyResponse,
    handlePayfortResponse,
} from "../services/payment.service.js";

export const tabbyResponse = async (request, reply) => {
    try {
        // console.log(request.query, 'requsetsaeras')
        const result = await handleTabbyResponse(request.query);
        return reply.redirect(result.url);
    } catch (err) {
        console.log("error from tabbyResponse controller", err);
        return errorResponse(reply, 500, err, [], "error");
    }
};

export const payfortResponse = async (request, reply) => {
    try {
        const result = await handlePayfortResponse(request.body);
        return reply.redirect(result.url);
    } catch (err) {
        console.log("error from payfortResponse controller", err);
        return errorResponse(reply, 500, err, [], "error");
    }
};
