import { successResponse, errorResponse } from "../utils/response.js";
import {
    productDetailsService,
    getRelatedItemsService,
} from "../services/product.service.js";

export const productDetails = async (request, reply) => {
    try {
        const result = await productDetailsService(request);

        return successResponse(
            reply,
            result.statusCode,
            result.message,
            result.status,
            result.result
        );
    } catch (err) {
        console.error("Error in productDetails controller:", err);
        return errorResponse(reply, 500, "Internal Server Error", [], "error");
    }
};

export const getRelatedItems = async (request, reply) => {
    try {
        const result = await getRelatedItemsService(request);

        return successResponse(
            reply,
            result.statusCode,
            result.message,
            result.status,
            result.result
        );
    } catch (err) {
        console.error("Error in getRelatedItems controller:", err);
        return errorResponse(reply, 500, "Internal Server Error", [], "error");
    }
};
