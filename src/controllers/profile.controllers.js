import { successResponse, errorResponse } from "../utils/response.js";
import {
    getUserWishlistService,
    addAndRemoveWishListService,
    getMyProfileService,
    updateMyProfileService,
    changePasswordService,
} from "../services/profile.service.js";

export const getUserWishlist = async (request, reply) => {
    try {
        const result = await getUserWishlistService(request);
        return successResponse(
            reply,
            result.statusCode,
            result.message,
            result.status,
            result.result
        );
        return result;
    } catch (err) {
        return errorResponse(reply, 500, err, {}, "error", request);
    }
};

export const addAndRemoveWishList = async (request, reply) => {
    try {
        const result = await addAndRemoveWishListService(request);
        return successResponse(
            reply,
            result.statusCode,
            result.message,
            result.status,
            result.result
        );
        return result;
    } catch (err) {
        return errorResponse(reply, 500, err, {}, "error", request);
    }
};

export const getMyProfile = async (request, reply) => {
    try {
        const result = await getMyProfileService(request);
        return successResponse(
            reply,
            result.statusCode,
            result.message,
            result.status,
            result.result
        );
        return result;
    } catch (err) {
        return errorResponse(reply, 500, err, {}, "error", request);
    }
};

export const updateMyProfile = async (request, reply) => {
    try {
        const result = await updateMyProfileService(request);
        return successResponse(
            reply,
            result.statusCode,
            result.message,
            result.status,
            result.result
        );
        return result;
    } catch (err) {
        return errorResponse(reply, 500, err, {}, "error", request);
    }
};

export const changePassword = async (request, reply) => {
    try {
        const result = await changePasswordService(request);
        return successResponse(
            reply,
            result.statusCode,
            result.message,
            result.status,
            result.result
        );
        return result;
    } catch (err) {
        return errorResponse(reply, 500, err, {}, "error", request);
    }
};
