import { errorResponse, successResponse } from "../utils/response.js";

import {
    addProductReview,
    deleteProductReview,
    editProductReview,
    getProductReviewsByProductId,
    getUserReviews,
    toggleDislikeOnR<PERSON>iew,
    toggleLikeOnReview,
} from "../services/reviews.service.js";

// ─────────────────────────────────────────────
// Add Review Controller
// ─────────────────────────────────────────────
export const addProductReviewController = async (request, reply) => {
    try {
        const result = await addProductReview(request);
        return successResponse(
            reply,
            result.statusCode,
            result.message,
            result.status,
            result.result
        );
    } catch (err) {
        return errorResponse(
            reply,
            500,
            err.message || "Failed to submit review.",
            {},
            "error",
            request
        );
    }
};

// ─────────────────────────────────────────────
// Edit Review Controller
// ─────────────────────────────────────────────
export const editProductReviewController = async (request, reply) => {
    try {
        const result = await editProductReview(request);
        return successResponse(
            reply,
            result.statusCode,
            result.message,
            result.status,
            result.result
        );
    } catch (err) {
        return errorResponse(
            reply,
            500,
            err.message || "Failed to update review.",
            {},
            "error",
            request
        );
    }
};

// ─────────────────────────────────────────────
// Delete Review Controller
// ─────────────────────────────────────────────
export const deleteProductReviewController = async (request, reply) => {
    try {
        const result = await deleteProductReview(request);
        return successResponse(
            reply,
            result.statusCode,
            result.message,
            result.status,
            result.result
        );
    } catch (err) {
        return errorResponse(
            reply,
            500,
            err.message || "Failed to delete review.",
            {},
            "error",
            request
        );
    }
};

export const getProductReviewByIdController = async (request, reply) => {
    try {
        const result = await getProductReviewsByProductId(request);

        const data = {
            result: result.result,
            userReview: result.userReview,
            stats: result.stats,
        };
        return successResponse(
            reply,
            result.statusCode,
            result.message,
            result.status,
            data
        );
    } catch (err) {
        return errorResponse(
            reply,
            500,
            err.message || "Failed to fetch review.",
            {},
            "error",
            request
        );
    }
};
export const getUserReviewsController = async (request, reply) => {
    try {
        const result = await getUserReviews(request);

        return successResponse(
            reply,
            result.statusCode,
            result.message,
            result.status,
            result.result
        );
    } catch (err) {
        return errorResponse(
            reply,
            500,
            err.message || "Failed to fetch review.",
            {},
            "error",
            request
        );
    }
};

// ─────────────────────────────────────────────
// Edit Review Controller
// ─────────────────────────────────────────────
export const updateVoteByIdController = async (request, reply) => {
    try {
        const result = await toggleLikeOnReview(request);
        return successResponse(
            reply,
            result.statusCode,
            result.message,
            result.status,
            result.result
        );
    } catch (err) {
        return errorResponse(
            reply,
            500,
            err.message || "Failed to update review.",
            {},
            "error",
            request
        );
    }
};
export const updateDownVoteByIdController = async (request, reply) => {
    try {
        const result = await toggleDislikeOnReview(request);
        return successResponse(
            reply,
            result.statusCode,
            result.message,
            result.status,
            result.result
        );
    } catch (err) {
        return errorResponse(
            reply,
            500,
            err.message || "Failed to update review.",
            {},
            "error",
            request
        );
    }
};
