import {
    searchProductsService,
    searchResultItemsService,
    appSearchproductsService,
    appSearchResultItemsService,
} from "../services/search.service.js";
import { successResponse, errorResponse } from "../utils/response.js";

export const searchProducts = async (request, reply) => {
    try {
        const result = await searchProductsService(request);

        return successResponse(
            reply,
            result.statusCode,
            result.message,
            result.success,
            result.data
        );
    } catch (err) {
        return errorResponse(reply, 500, err, [], "error", request);
    }
};

export const searchResultItems = async (request, reply) => {
    try {
        const result = await searchResultItemsService(request);

        return successResponse(
            reply,
            result.statusCode,
            result.message,
            result.success,
            result.data
        );
    } catch (err) {
        return errorResponse(reply, 500, err, [], "error", request);
    }
};

export const appSearchproducts = async (request, reply) => {
    try {
        const result = await appSearchproductsService(request);

        return successResponse(
            reply,
            result.statusCode,
            result.message,
            result.success,
            result.data
        );
    } catch (err) {
        return errorResponse(reply, 500, err, [], "error", request);
    }
};

export const appSearchResultItems = async (request, reply) => {
    try {
        const result = await appSearchResultItemsService(request);

        return successResponse(
            reply,
            result.statusCode,
            result.message,
            result.success,
            result.data
        );
    } catch (err) {
        return errorResponse(reply, 500, err, [], "error", request);
    }
};
