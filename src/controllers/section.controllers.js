import {
    getBlogsService,
    getBlogByCategoryIdService,
    clearanceSaleService,
    dealOfTheDayService,
    topSellingProductsService,
    saverZoneService,
    metaTagService,
    getInfinteScrollItemsService,
} from "../services/section.service.js";
import { successResponse, errorResponse } from "../utils/response.js";

import {
    getSpecialDealsProductsELK,
    getComboDealsProductsELK,
    eidResultELK,
} from "../elk/section.elk.js";

export const getBlogs = async (request, reply) => {
    try {
        const result = await getBlogsService(request);

        if (result) {
            return successResponse(
                reply,
                200,
                "Blogs fetched Successfully",
                "success",
                result
            );
        } else {
            return successResponse(reply, 200, "Data not found", "failure", []);
        }
    } catch (err) {
        return errorResponse(reply, 500, err, [], "error", request);
    }
};

export const getBlogByCategoryId = async (request, reply) => {
    try {
        const result = await getBlogByCategoryIdService(request);

        if (result) {
            return successResponse(
                reply,
                200,
                "Blogs fetched Successfully",
                "success",
                result
            );
        } else {
            return successResponse(reply, 200, "Data not found", "error", []);
        }
    } catch (err) {
        return errorResponse(reply, 500, err, [], "error", request);
    }
};

export const clearanceSale = async (request, reply) => {
    try {
        const result = await clearanceSaleService(request);

        if (result) {
            return successResponse(
                reply,
                200,
                "Clearance sale fetched Successfully",
                "success",
                result
            );
        } else {
            return successResponse(reply, 200, "Data not found", "error", []);
        }
    } catch (err) {
        return errorResponse(reply, 500, err, [], "error", request);
    }
};

export const dealOfTheDay = async (request, reply) => {
    try {
        const result = await dealOfTheDayService(request);

        if (result) {
            return successResponse(
                reply,
                200,
                "Deal of the day fetched Successfully",
                "success",
                result
            );
        } else {
            return successResponse(reply, 200, "Data not found", "failure", []);
        }
    } catch (err) {
        console.log("err", err);
        return errorResponse(reply, 500, err, [], "error", request);
    }
};

export const topSellingProducts = async (request, reply) => {
    try {
        const result = await topSellingProductsService(request);

        if (result) {
            return successResponse(
                reply,
                200,
                "Deal of the day fetched Successfully",
                "success",
                result
            );
        } else {
            return successResponse(reply, 200, "Data not found", "failure", []);
        }
    } catch (err) {
        return errorResponse(reply, 500, err, [], "error", request);
    }
};

export const saverZone = async (request, reply) => {
    try {
        const result = await saverZoneService(request);

        if (result) {
            return successResponse(
                reply,
                200,
                "Deal of the day fetched Successfully",
                "success",
                result
            );
        } else {
            return successResponse(reply, 200, "Data not found", "failure", []);
        }
    } catch (err) {
        return errorResponse(reply, 500, err, [], "error", request);
    }
};

export const metaTags = async (request, reply) => {
    try {
        const result = await metaTagService(request);
        if (result) {
            return successResponse(
                reply,
                200,
                "Deal of the day fetched Successfully",
                "success",
                result
            );
        } else {
            return successResponse(reply, 200, "Data not found", "failure", []);
        }
    } catch (err) {
        return errorResponse(reply, 500, err, [], "error", request);
    }
};

// New ELK Controllers

/**
 * Get Special Deals Products from Elasticsearch
 */
export const getSpecialDealsProducts = async (request, reply) => {
    try {
        const { sectionId } = request.params;
        const { limit = 10, offset = 0 } = request.query;

        // Validate sectionId
        if (!sectionId || sectionId === "undefined" || sectionId === "null") {
            return errorResponse(
                reply,
                400,
                "Section ID is required",
                [],
                "error",
                request
            );
        }

        const numericSectionId = parseInt(sectionId);
        if (isNaN(numericSectionId)) {
            return errorResponse(
                reply,
                400,
                "Section ID must be a valid number",
                [],
                "error",
                request
            );
        }

        console.log(
            "Controller received sectionId:",
            sectionId,
            "parsed as:",
            numericSectionId
        );

        // You may need to define this array based on your business logic
        const get_special_deal_check_in_sec_id = [1, 2, 3]; // Example section IDs

        const result = await getSpecialDealsProductsELK(
            numericSectionId,
            parseInt(limit) || 10,
            parseInt(offset) || 0,
            request,
            get_special_deal_check_in_sec_id
        );

        if (result && result.length > 0) {
            return successResponse(
                reply,
                200,
                "Special deals products fetched successfully",
                "success",
                result
            );
        } else {
            return successResponse(
                reply,
                200,
                "No special deals products found",
                "success",
                []
            );
        }
    } catch (err) {
        console.error("Error in getSpecialDealsProducts:", err);
        return errorResponse(reply, 500, err.message, [], "error", request);
    }
};

/**
 * Get Combo Deals Products from Elasticsearch
 */
export const getComboDealsProducts = async (request, reply) => {
    try {
        const { limit = 10, from = 0 } = request.query;
        const { sec_id } = request.params;

        // Validate sec_id
        if (!sec_id || sec_id === "undefined" || sec_id === "null") {
            return errorResponse(
                reply,
                400,
                "Section ID is required",
                [],
                "error",
                request
            );
        }

        const numericSecId = parseInt(sec_id);
        if (isNaN(numericSecId)) {
            return errorResponse(
                reply,
                400,
                "Section ID must be a valid number",
                [],
                "error",
                request
            );
        }

        console.log(
            "Controller received sec_id:",
            sec_id,
            "parsed as:",
            numericSecId
        );

        // Add sec_id to request object for the ELK function
        request.sec_id = numericSecId;

        const result = await getComboDealsProductsELK(
            request,
            parseInt(limit) || 10,
            parseInt(from) || 0
        );

        if (result && result.length > 0) {
            return successResponse(
                reply,
                200,
                "Combo deals products fetched successfully",
                "success",
                result
            );
        } else {
            return successResponse(
                reply,
                200,
                "No combo deals products found",
                "success",
                []
            );
        }
    } catch (err) {
        console.error("Error in getComboDealsProducts:", err);
        return errorResponse(reply, 500, err.message, [], "error", request);
    }
};

/**
 * Get EID Result Products from Elasticsearch
 */
export const getEidResultProducts = async (request, reply) => {
    try {
        const result = await eidResultELK(request);

        if (result && result.length > 0) {
            return successResponse(
                reply,
                200,
                "EID result products fetched successfully",
                "success",
                result
            );
        } else {
            return successResponse(
                reply,
                200,
                "No EID result products found",
                "success",
                []
            );
        }
    } catch (err) {
        console.error("Error in getEidResultProducts:", err);
        return errorResponse(reply, 500, err.message, [], "error", request);
    }
};

export const getInfinteScrollItems = async (request, reply) => {
    try {
        const result = await getInfinteScrollItemsService(request);

        if (result) {
            return successResponse(
                reply,
                200,
                "Data fetched Successfully",
                "success",
                result
            );
        } else {
            return successResponse(reply, 200, "Data not found", "failure", []);
        }
    } catch (err) {
        return errorResponse(reply, 500, err, [], "error", request);
    }
};
