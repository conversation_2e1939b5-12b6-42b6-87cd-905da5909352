import { successResponse, errorResponse } from "./../utils/response.js";
import {
    filteredItemsService,
    getAllSubCategoryItemsService,
} from "../services/subCategory.service.js";

export const filteredItems = async (request, reply) => {
    try {
        const result = await filteredItemsService(request);
        return successResponse(
            reply,
            200,
            "Data fetched Successfully",
            "success",
            result
        );
    } catch (err) {
        console.error("ERROR in filteredItems controller:", err.message);
        return errorResponse(reply, 500, err, [], "error", request);
    }
};

export const getAllSubCategoryItems = async (request, reply) => {
    try {
        const result = await getAllSubCategoryItemsService(request);
        return successResponse(
            reply,
            200,
            "Data fetched Successfully",
            "success",
            result
        );
    } catch (err) {
        return errorResponse(reply, 500, err, [], "error", request);
    }
};
