import { Sequelize } from "sequelize";
import dotenv from "dotenv";

dotenv.config({
    path: "./.env",
});

const sequelize = new Sequelize({
    database: process.env.DB_DATABASE,
    dialect: "mysql",
    replication: {
        read: [
            {
                host: process.env.DB_HOST,
                username: process.env.DB_USER,
                password: process.env.DB_PASSWORD,
            },
        ],
        write: {
            host: process.env.DB_HOST,
            username: process.env.DB_USER,
            password: process.env.DB_PASSWORD,
        },
    },
    pool: {
        max: 15,
        min: 3,
        acquire: 20000, // wait max 20s before timing out connection request
        idle: 15000, // release idle connections after 15s
    },
    define: {
        timestamps: false,
        underscored: true,
    },
    logging: false,
});

export default sequelize;
