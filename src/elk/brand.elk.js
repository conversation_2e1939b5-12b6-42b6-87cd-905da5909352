import { getElasticProductTransform } from "./common.elk.js";

export const getAllFilteredBrandItemsElkOLD = async (
    req,
    brand_name,
    fastify
) => {
    const { subcategory, subcategory_id, page = 1 } = req.query;
    const pagination = parseInt(process.env.ELASTIC_PAGINATE || "10", 10);
    const from = (parseInt(page, 10) - 1) * pagination;
    const client = fastify.elasticsearch;

    const must = [{ match: { brand_name } }, { term: { status: 1 } }];

    if (subcategory || subcategory_id) {
        must.push({ match: { subcategory_id } });
    }

    // Inventory-specific conditions moved into nested block
    must.push({
        nested: {
            path: "inventory",
            query: {
                bool: {
                    must: [
                        { term: { "inventory.country_id": req.country_id } },
                        { term: { "inventory.rstatus": 1 } },
                        { match_phrase: { "inventory.stock": "In stock" } },
                    ],
                },
            },
        },
    });

    try {
        const result = await client.search({
            index: process.env.ELASTIC_PRODUCT_INDEX,
            body: {
                from,
                size: pagination,

                _source: {
                    includes: [
                        "id",
                        "sku",
                        "name",
                        "url",
                        "image",
                        "brand_id",
                        "brand_name",
                        "inventory.inventory_id",
                        "inventory.country_id",
                        "inventory.quantity",
                        "inventory.selling_price",
                        "inventory.special_price",
                        "inventory.promotion_price",
                        "inventory.from_date",
                        "inventory.to_date",
                        "inventory.price",
                    ],
                },

                sort: [
                    { updated_date: { order: "desc" } },
                    { position: { order: "asc" } },
                ],

                query: {
                    bool: {
                        must,
                    },
                },

                aggs: {
                    max_price: {
                        nested: {
                            path: "inventory",
                        },
                        aggs: {
                            value: {
                                max: {
                                    field: "inventory.special_price",
                                },
                            },
                        },
                    },
                    colors: {
                        terms: {
                            field: "color_id",
                            size: 1000,
                        },
                        aggs: {
                            docs: {
                                top_hits: {
                                    _source: ["color_id", "color_name"],
                                    size: 1,
                                },
                            },
                        },
                    },
                    categories: {
                        terms: {
                            field: "id",
                            size: 100000,
                        },
                        aggs: {
                            docs: {
                                top_hits: {
                                    _source: [
                                        "subcategory_id",
                                        "sub_sub_category_id",
                                        "category_id",
                                        "category_name",
                                        "subcategory_name",
                                        "sub_sub_category_name",
                                    ],
                                    size: 1,
                                },
                            },
                        },
                    },
                },
            },
        });

        // console.log('result', result)
        return {
            categories: result.aggregations?.categories?.buckets || [],
            max_price: result.aggregations?.max_price?.value?.value || 0,
            colors: result.aggregations?.colors?.buckets || [],
            products: (await getElasticProductTransform(result)) || [],
            // products: result.hits?.hits?.map((h) => h._source) || [],
        };
    } catch (error) {
        throw error;

        return {
            categories: [],
            max_price: 0,
            colors: [],
            products: [],
        };
    }
};

export const getAllFilteredBrandItemsElk = async (req, brand_name, fastify) => {
    const { subcategory_id, page = 1 } = req.query;
    const pagination = Number.parseInt(
        process.env.ELASTIC_PAGINATE || "10",
        10
    );
    const from = (Number.parseInt(page, 10) - 1) * pagination;
    const client = fastify.elasticsearch;

    // Build must conditions
    const must = [
        // Prefer keyword/term to avoid analyzer fuzziness on exact brand names
        { term: { "brand_name.keyword": brand_name } },
        { term: { status: 1 } },
    ];

    // Filter by subcategory if provided
    if (subcategory_id) {
        must.push({ term: { subcategory_id: Number(subcategory_id) } });
    }

    // Nested inventory filter + inner_hits (size:1) for the country
    must.push({
        nested: {
            path: "inventory",
            query: {
                bool: {
                    must: [
                        { term: { "inventory.country_id": req.country_id } },
                        { term: { "inventory.rstatus": 1 } },
                        { match_phrase: { "inventory.stock": "In stock" } },
                    ],
                },
            },
            inner_hits: {
                size: 1,
                _source: {
                    includes: [
                        "inventory.inventory_id",
                        "inventory.country_id",
                        "inventory.quantity",
                        "inventory.selling_price",
                        "inventory.special_price",
                        "inventory.promotion_price",
                        "inventory.from_date",
                        "inventory.to_date",
                        "inventory.price",
                    ],
                },
            },
        },
    });

    try {
        const result = await client.search({
            index: process.env.ELASTIC_PRODUCT_INDEX,
            body: {
                from,
                size: pagination,

                // Only root (product) fields here
                _source: [
                    "id",
                    "sku",
                    "name",
                    "url",
                    "image",
                    "brand_id",
                    "brand_name",
                ],

                sort: [
                    { updated_date: { order: "desc" } },
                    { position: { order: "asc" } },
                ],

                query: { bool: { must } },

                aggs: {
                    max_price: {
                        nested: { path: "inventory" },
                        aggs: {
                            value: {
                                max: { field: "inventory.special_price" },
                            },
                        },
                    },
                    colors: {
                        terms: { field: "color_id", size: 1000 },
                        aggs: {
                            docs: {
                                top_hits: {
                                    _source: ["color_id", "color_name"],
                                    size: 1,
                                },
                            },
                        },
                    },
                    categories: {
                        terms: { field: "id", size: 100000 },
                        aggs: {
                            docs: {
                                top_hits: {
                                    _source: [
                                        "subcategory_id",
                                        "sub_sub_category_id",
                                        "category_id",
                                        "category_name",
                                        "subcategory_name",
                                        "sub_sub_category_name",
                                    ],
                                    size: 1,
                                },
                            },
                        },
                    },
                },
            },
        });

        return {
            categories: result.aggregations?.categories?.buckets || [],
            max_price: result.aggregations?.max_price?.value?.value || 0,
            colors: result.aggregations?.colors?.buckets || [],
            products: (await getElasticProductTransform(result)) || [],
        };
    } catch (error) {
        // Surface ES error to your error handler
        throw error;
    }
};

export const getBrandsBySubCategoryElk = async (subcategory_id, fastify) => {
    const client = fastify.elasticsearch;

    try {
        const result = await client.search({
            index: process.env.ELASTIC_PRODUCT_INDEX,
            body: {
                _source: false,
                query: {
                    bool: {
                        must: [{ match: { subcategory_id } }],
                    },
                },
                aggs: {
                    brands: {
                        terms: {
                            field: "brand_id",
                        },
                        aggs: {
                            docs: {
                                top_hits: {
                                    _source: [
                                        "brand_id",
                                        "brand_name",
                                        "brand_image",
                                        "brand_url",
                                    ],
                                    size: 1,
                                },
                            },
                        },
                    },
                },
            },
        });

        return {
            brands: result.aggregations?.brands?.buckets || [],
        };
    } catch (err) {
        throw err;
        return { brands: [] };
    }
};
