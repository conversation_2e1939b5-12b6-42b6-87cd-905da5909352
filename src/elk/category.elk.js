import fastify from "../app.js";

export const getAllCategoriesElk = async (fastify, options = {}) => {
    try {
        const {
            size = 100,
            from = 0,
            categoryLevel = null,
            parentId = null,
            frontView = null,
            status = 1,
        } = options;

        const mustQuery = [];

        if (status !== null) {
            mustQuery.push({ match: { rstatus: status.toString() } });
        }

        if (categoryLevel !== null) {
            mustQuery.push({
                match: { category_level: categoryLevel.toString() },
            });
        }

        if (parentId !== null) {
            mustQuery.push({ match: { categorypid: parentId.toString() } });
        }

        if (frontView !== null) {
            mustQuery.push({ match: { front_view: frontView.toString() } });
        }

        const client = await fastify.elasticsearch;

        const searchQuery = {
            index: process.env.ELASTIC_CATEGORY_INDEX,
            body: {
                from: from,
                size: size,
                _source: {
                    includes: [
                        "categoryid",
                        "categorypid",
                        "category_name",
                        "slug",
                        "position",
                        "rstatus",
                        "category_level",
                        "front_view",
                        "category_images.vector_icon",
                        "category_images.image",

                        "childCategory.categoryid",
                        "childCategory.categorypid",
                        "childCategory.category_name",
                        "childCategory.slug",
                        "childCategory.category_images.image",

                        "childCategory.brand_detail.brandid",
                        "childCategory.brand_detail.brand_name",
                        "childCategory.brand_detail.brand_url",
                        "childCategory.brand_detail.brand_image",
                        // "childCategory.brand_detail.total_product",

                        "childCategory.childCategory.categoryid",
                        "childCategory.childCategory.categorypid",
                        "childCategory.childCategory.category_name",
                        "childCategory.childCategory.slug",
                        "childCategory.childCategory.category_images.image",
                    ],
                },
                sort: [
                    { position: { order: "asc" } },
                    { categoryid: { order: "asc" } },
                ],
                query: {
                    bool: {
                        must: mustQuery,
                    },
                },
            },
        };

        const result = await client.search(searchQuery);
        const CDN_IMAGE_BASE_URL = process.env.CDN_IMAGE_BASE_URL;

        return result.hits.hits.map((hit) => {
            const source = hit._source;
            let vectorIcon = null;
            if (source.category_images && source.category_images.vector_icon) {
                const iconData = source.category_images.vector_icon;

                // Handle array case (which is what we're getting)
                if (Array.isArray(iconData) && iconData.length > 0) {
                    const firstIcon = iconData[0];
                    if (typeof firstIcon === "string") {
                        vectorIcon = firstIcon;
                    } else if (
                        typeof firstIcon === "object" &&
                        firstIcon !== null
                    ) {
                        vectorIcon =
                            firstIcon.original || firstIcon.file_url || null;
                    }
                }
                // Handle string case
                else if (typeof iconData === "string") {
                    vectorIcon = iconData;
                }
                // Handle object case
                else if (typeof iconData === "object" && iconData !== null) {
                    vectorIcon = iconData.original || iconData.file_url || null;
                }
            }
            let vectorIconUrl = "";
            if (vectorIcon && typeof vectorIcon === "string") {
                if (vectorIcon.startsWith("http")) {
                    vectorIconUrl = vectorIcon;
                } else if (vectorIcon.length > 0) {
                    vectorIconUrl = CDN_IMAGE_BASE_URL + vectorIcon;
                }
            }

            const filteredCategory = {
                category_id: source.categoryid,
                category_name: source.category_name,
                url: source.slug,
                vector_icon: vectorIconUrl,
                subcategory: [],
            };

            if (source.childCategory && Array.isArray(source.childCategory)) {
                filteredCategory.subcategory = source.childCategory.map(
                    (child) => {
                        let subCategoryImage = null;
                        if (
                            child.category_images &&
                            child.category_images.image
                        ) {
                            // Handle image structure according to ELK mapping
                            const imageData = child.category_images.image;

                            // Extract the actual string value, not the object
                            if (typeof imageData === "string") {
                                subCategoryImage = imageData;
                            } else if (
                                Array.isArray(imageData) &&
                                imageData.length > 0
                            ) {
                                // Handle if it's an array
                                const firstImage = imageData[0];
                                if (typeof firstImage === "string") {
                                    subCategoryImage = firstImage;
                                } else if (
                                    typeof firstImage === "object" &&
                                    firstImage !== null
                                ) {
                                    subCategoryImage =
                                        firstImage["1"] ||
                                        firstImage["0"] ||
                                        firstImage.original ||
                                        firstImage.file_url ||
                                        null;
                                }
                            } else if (
                                typeof imageData === "object" &&
                                imageData !== null
                            ) {
                                subCategoryImage =
                                    imageData["1"] || // file_url numeric key
                                    imageData["0"] || // original numeric key
                                    imageData.original || // original property
                                    imageData.file_url || // file_url property
                                    null;
                            }
                        }

                        // Extract brand details from nested brand_detail field
                        const validExtensions = [
                            ".jpg",
                            ".jpeg",
                            ".png",
                            ".webp",
                            ".svg",
                        ];

                        const brands = [];
                        if (
                            child.brand_detail &&
                            Array.isArray(child.brand_detail)
                        ) {
                            child.brand_detail.forEach((brand) => {
                                const img = brand?.brand_image
                                    ? brand.brand_image.trim()
                                    : "";
                                const hasValidExt =
                                    img &&
                                    validExtensions.some((ext) =>
                                        img.toLowerCase().endsWith(ext)
                                    );

                                if (
                                    brand.brandid &&
                                    brand.brandid !== 38 &&
                                    brand.brandid !== 0 &&
                                    hasValidExt
                                ) {
                                    brands.push({
                                        brand_id: brand.brandid,
                                        // count: brand.total_product,
                                        subcategory_id: child.categoryid,
                                        name: brand.brand_name || "",
                                        url: brand.brand_url || "",
                                        image: img.startsWith("http")
                                            ? img
                                            : CDN_IMAGE_BASE_URL + img,
                                    });
                                }
                            });
                        }

                        const filteredChild = {
                            category_id: child.categorypid,
                            sub_category_id: child.categoryid,
                            sub_category_name: child.category_name,
                            sub_category_image:
                                subCategoryImage &&
                                typeof subCategoryImage === "string"
                                    ? CDN_IMAGE_BASE_URL + subCategoryImage
                                    : "",
                            url: child.slug,
                            sub_subcategory: [],
                            brands: brands.sort((a, b) => b.count - a.count), // Sort by count descending
                        };

                        if (
                            child.childCategory &&
                            Array.isArray(child.childCategory)
                        ) {
                            filteredChild.sub_subcategory =
                                child.childCategory.map((grandChild) => {
                                    let subSubCategoryImage = null;
                                    if (
                                        grandChild.category_images &&
                                        grandChild.category_images.image
                                    ) {
                                        // Handle sub-sub-category image structure according to ELK mapping
                                        const imageData =
                                            grandChild.category_images.image;

                                        // Extract the actual string value, not the object
                                        if (typeof imageData === "string") {
                                            subSubCategoryImage = imageData;
                                        } else if (
                                            Array.isArray(imageData) &&
                                            imageData.length > 0
                                        ) {
                                            // Handle if it's an array
                                            const firstImage = imageData[0];
                                            if (
                                                typeof firstImage === "string"
                                            ) {
                                                subSubCategoryImage =
                                                    firstImage;
                                            } else if (
                                                typeof firstImage ===
                                                    "object" &&
                                                firstImage !== null
                                            ) {
                                                subSubCategoryImage =
                                                    firstImage["1"] ||
                                                    firstImage["0"] ||
                                                    firstImage.original ||
                                                    firstImage.file_url ||
                                                    null;
                                            }
                                        } else if (
                                            typeof imageData === "object" &&
                                            imageData !== null
                                        ) {
                                            subSubCategoryImage =
                                                imageData["1"] || // file_url numeric key
                                                imageData["0"] || // original numeric key
                                                imageData.original || // original property
                                                imageData.file_url || // file_url property
                                                null;
                                        }
                                    }

                                    return {
                                        sub_category_id: grandChild.categorypid,
                                        sub_subcategory_id:
                                            grandChild.categoryid,
                                        sub_subcategory_name:
                                            grandChild.category_name,
                                        sub_subcategory_image:
                                            subSubCategoryImage &&
                                            typeof subSubCategoryImage ===
                                                "string"
                                                ? CDN_IMAGE_BASE_URL +
                                                  subSubCategoryImage
                                                : "",
                                        url: `${child.slug}/${grandChild.slug}`,
                                    };
                                });
                        }

                        return filteredChild;
                    }
                );
            }

            return filteredCategory;
        });
    } catch (error) {
        throw error;
    }
};

export const productCategoryElk = async (req) => {
    const countryId = req.country_id;
    const client = await fastify.elasticsearch;
    const result = await client.search({
        index: process.env.ELASTIC_PRODUCT_INDEX,
        body: {
            size: 0,
            query: {
                bool: {
                    must: [
                        {
                            match: {
                                "category_url.keyword": req.query.cat_url,
                            },
                        },
                        {
                            term: {
                                status: 1,
                            },
                        },
                        {
                            range: {
                                subcategory_id: {
                                    gte: 1,
                                },
                            },
                        },
                        {
                            nested: {
                                path: "inventory",
                                query: {
                                    bool: {
                                        must: [
                                            {
                                                term: {
                                                    "inventory.country_id":
                                                        countryId,
                                                },
                                            },
                                            {
                                                term: {
                                                    "inventory.rstatus": 1,
                                                },
                                            },
                                            {
                                                match_phrase: {
                                                    "inventory.stock":
                                                        "In stock",
                                                },
                                            },
                                        ],
                                    },
                                },
                                inner_hits: {
                                    size: 1,
                                    _source: {
                                        includes: [
                                            "inventory.inventory_id",
                                            "inventory.country_id",
                                            "inventory.price",
                                            "inventory.special_price",
                                            "inventory.selling_price",
                                            "inventory.quantity",
                                            "inventory.stock",
                                            "inventory.from_date",
                                            "inventory.to_date",
                                            "inventory.promotion_price",
                                        ],
                                    },
                                },
                            },
                        },
                    ],
                },
            },
            aggs: {
                categories: {
                    terms: {
                        field: "subcategory_id",
                        size: 50,
                    },
                    aggs: {
                        docs: {
                            top_hits: {
                                size: 20,
                                sort: {
                                    position: "asc",
                                    updated_date: "desc",
                                },
                            },
                        },
                    },
                },
                top_brands: {
                    terms: {
                        field: "brand_id",
                        size: 100000,
                    },
                    aggs: {
                        docs: {
                            top_hits: {
                                size: 1,
                                _source: [
                                    "brand_id",
                                    "brand_name",
                                    "brand_image",
                                    "brand_url",
                                ],
                            },
                        },
                    },
                },
                hot_deals: {
                    filter: {
                        bool: {
                            must: [
                                { term: { status: 1 } },
                                {
                                    nested: {
                                        path: "inventory",
                                        query: {
                                            bool: {
                                                must: [
                                                    {
                                                        term: {
                                                            "inventory.country_id":
                                                                countryId,
                                                        },
                                                    },
                                                    {
                                                        term: {
                                                            "inventory.rstatus": 1,
                                                        },
                                                    },
                                                    {
                                                        match_phrase: {
                                                            "inventory.stock":
                                                                "In stock",
                                                        },
                                                    },

                                                    {
                                                        range: {
                                                            "inventory.offer_from":
                                                                { lte: "now" },
                                                        },
                                                    },
                                                ],
                                            },
                                        },
                                        inner_hits: {
                                            size: 1,
                                            _source: {
                                                includes: [
                                                    "inventory.inventory_id",
                                                    "inventory.country_id",
                                                    "inventory.price",
                                                    "inventory.special_price",
                                                    "inventory.selling_price",
                                                    "inventory.quantity",
                                                    "inventory.stock",
                                                    "inventory.offer_from",
                                                    "inventory.offer_to",
                                                    "inventory.promotion_price",
                                                ],
                                            },
                                        },
                                    },
                                },
                            ],
                        },
                    },
                    aggs: {
                        docs: {
                            top_hits: {
                                size: 25,
                                sort: [{ offer_from: "desc" }],
                                _source: {
                                    includes: [
                                        "id",
                                        "name",
                                        "image",
                                        "url",
                                        "sku",
                                        "brand_id",
                                        "subcategory_id",
                                        "subcategory_name",
                                        "subcategory_url",
                                        "category_id",
                                    ],
                                },
                            },
                        },
                    },
                },
            },
        },
    });

    return {
        product_categories: result.aggregations.categories.buckets,
        top_brands: result.aggregations.top_brands.buckets,
        hot_deals: result.aggregations.hot_deals.docs.hits,
    };
};

export const searchProductsByCategory = async (req) => {
    const { cat_url } = req.query || "Perfumes";
    const countryId = req.country_id;
    const client = fastify.elasticsearch;
    const indexName = process.env.ELASTIC_PRODUCT_INDEX;

    const queryBody = {
        _source: ["id", "sku", "name", "url", "image"],
        from: req.query.page == "1" ? 0 : (req.query.page - 1) * 20,
        size: 20,
        query: {
            bool: {
                must: [
                    { term: { "category_url.keyword": cat_url } },

                    {
                        nested: {
                            path: "inventory",
                            query: {
                                bool: {
                                    must: [
                                        {
                                            term: {
                                                "inventory.country_id":
                                                    countryId,
                                            },
                                        },
                                        { term: { "inventory.rstatus": 1 } },
                                        {
                                            match_phrase: {
                                                "inventory.stock": "In stock",
                                            },
                                        },
                                    ],
                                },
                            },
                            inner_hits: {
                                name: "inventory_hit",
                                size: 1,
                                _source: [
                                    "inventory.price",
                                    "inventory.selling_price",
                                    "inventory.special_price",
                                    "inventory.promotion_price",
                                    "inventory.from_date",
                                    "inventory.to_date",
                                ],
                                sort: [{ "inventory.price": { order: "asc" } }],
                            },
                        },
                    },
                ],
            },
        },
    };

    // console.log('queryBody', JSON.stringify(queryBody))
    const result = await client.search({ index: indexName, body: queryBody });

    return result;
};
