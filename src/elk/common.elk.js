import moment from "moment";
import { transformProductItem } from "../services/common.service.js";
import { CURRENCY, OURSHOPEE_VENDOR_DETAILS } from "../region/config.js";
import { getAllVendorsDetailsById } from "../services/vendor.service.js";

// get_products_by_section_elk
export const getProductsBySectionElk = async (req, inputData) => {
    try {
        const must_array = [{ term: { status: 1 } }];
        const now = new Date();
        const dateTimeIso = moment.utc(now).format("YYYY-MM-DDTHH:mm:ss") + "Z";

        const inventory_must = [
            { term: { "inventory.country_id": req.country_id } },
            { term: { "inventory.rstatus": 1 } },
        ];

        let sort_order = [
            { updated_date: { order: "desc" } },
            { id: { order: "desc" } },
        ];

        if (inputData.condition_from_date === true) {
            inventory_must.push({
                range: {
                    "inventory.from_date": {
                        lte: dateTimeIso,
                    },
                },
            });
        }

        if (inputData.condition_quantity === true) {
            inventory_must.push({
                range: { "inventory.special_price": { gte: 1 } },
            });

            inventory_must.push({
                match_phrase: { "inventory.stock": "In stock" },
            });

            sort_order = [{ id: { order: "desc" } }];
        }

        if (inputData.section_id && inputData.section_id !== 0) {
            must_array.push({ term: { section_id: inputData.section_id } });
        }

        if (inputData.product_id && inputData.product_id !== 0) {
            must_array.push({ term: { product_id: inputData.product_id } });
        }

        if (inputData.front_view && inputData.front_view !== 0) {
            must_array.push({ term: { front_view: 1 } });
        }

        must_array.push({
            nested: {
                path: "inventory",
                query: {
                    bool: {
                        must: inventory_must,
                    },
                },
                inner_hits: {
                    _source: [
                        "inventory.inventory_id",
                        "inventory.quantity",
                        "inventory.special_price",
                        "inventory.promotion_price",
                        "inventory.from_date",
                        "inventory.to_date",
                        "inventory.price",
                        "inventory.selling_price",
                        "inventory.country_id",
                    ],
                    size: 1,
                },
            },
        });

        const client = await req.server.elasticsearch;

        const result = await client.search({
            index: process.env.ELASTIC_PRODUCT_INDEX,
            body: {
                from: inputData.offset || 0,
                size: inputData.limit || 10,
                _source: ["id", "sku", "name", "url", "image"],
                sort: sort_order,
                query: {
                    bool: {
                        must: must_array,
                    },
                },
            },
        });

        const transformedResult = getElasticProductTransform(result) || [];

        return transformedResult;
    } catch (err) {
        console.log(err);
        throw err;
    }
};

// get_products_by_ids_elk
export const getProductsByIdsElk = async (req, productIds = []) => {
    let pIds = [],
        vendorIds = [];

    productIds.forEach((p) => {
        p.productId && pIds.push(p.productId);
        (p.vendorId || p.vendorId == 0) && vendorIds.push(p.vendorId);
    });

    if (!Array.isArray(pIds) || pIds.length === 0) {
        return [];
    }
    const client = await req.server.elasticsearch;

    const result = await client.search({
        index: process.env.ELASTIC_PRODUCT_INDEX,
        size: 10000,
        _source: [
            "id",
            "sku",
            "name",
            "url",
            "image",
            "min_pur",
            "subcategory_id",
            "brand_name",
        ],
        body: {
            query: {
                bool: {
                    must: [
                        { terms: { id: pIds } },
                        { term: { status: 1 } },
                        {
                            nested: {
                                path: "inventory",
                                query: {
                                    bool: {
                                        must: [
                                            {
                                                term: {
                                                    "inventory.country_id":
                                                        req.country_id,
                                                },
                                            },
                                            {
                                                term: {
                                                    "inventory.rstatus": 1,
                                                },
                                            },
                                            {
                                                match_phrase: {
                                                    "inventory.stock":
                                                        "In stock",
                                                },
                                            },
                                            {
                                                range: {
                                                    "inventory.quantity": {
                                                        gt: 0,
                                                    },
                                                },
                                            },
                                            {
                                                terms: {
                                                    "inventory.vendor_id":
                                                        vendorIds,
                                                },
                                            },
                                        ],
                                    },
                                },
                                inner_hits: {
                                    _source: {
                                        includes: [
                                            "inventory.inventory_id",
                                            "inventory.country_id",
                                            "inventory.quantity",
                                            "inventory.selling_price",
                                            "inventory.special_price",
                                            "inventory.promotion_price",
                                            "inventory.from_date",
                                            "inventory.to_date",
                                            "inventory.price",
                                            "inventory.shipping_charge",
                                            "inventory.stock",
                                            "inventory.vendor_id",
                                        ],
                                    },
                                    size: 10,
                                },
                            },
                        },
                    ],
                },
            },
        },
    });

    const currency = CURRENCY[req.country_id].currency;

    return (
        result?.hits?.hits?.flatMap((hit) => {
            const product = hit._source;
            let inventory_hit = hit.inner_hits?.inventory?.hits?.hits || [];

            // Return one row per vendorId in productIds
            return productIds
                .filter((p) => p.productId === product.id)
                .map((p) => {
                    let inventory =
                        inventory_hit.find(
                            (inv) => inv._source?.vendor_id === p.vendorId
                        )?._source || {};

                    const cleanedProduct = { ...product, ...inventory };

                    return {
                        promotion_price: cleanedProduct.promotion_price,
                        quantity: cleanedProduct.quantity,
                        shipping_charge: cleanedProduct.shipping_charge,
                        price: cleanedProduct.price,
                        special_price: cleanedProduct.special_price,
                        to_date: cleanedProduct.to_date,
                        from_date: cleanedProduct.from_date,
                        brand_name: cleanedProduct.brand_name,
                        stock: cleanedProduct.stock,
                        subcategory_id: cleanedProduct.subcategory_id,
                        vendor_id: cleanedProduct.vendor_id,
                        ...transformProductItem(cleanedProduct, currency),
                    };
                });
        }) || []
    );
};

export const getElasticProductTransform = async (result) => {
    const cleaned = result.hits.hits.map((hit) => {
        const product = hit._source;

        const inventoryData =
            hit.inner_hits?.inventory?.hits?.hits?.[0]?._source || null;

        const fallbackInventory = Array.isArray(product.inventory)
            ? product.inventory[0]
            : null;

        // final inventory: prefer `inner_hits`, fallback to top-level inventory[0]
        let inventory = inventoryData || fallbackInventory;

        // Remove 'inventory.' prefix from keys if it exists
        if (inventory) {
            const cleanedInventory = {};
            Object.keys(inventory).forEach((key) => {
                const cleanKey = key.startsWith("inventory.")
                    ? key.replace("inventory.", "")
                    : key;
                cleanedInventory[cleanKey] = inventory[key];
            });
            inventory = cleanedInventory;
        }

        delete product.inventory;

        return {
            ...product,
            ...inventory,
        };
    });

    return cleaned;
};

export const getElasticResultProductTransform = (hits) => {
    return hits.hits.map((hit) => {
        const product = hit._source;
        const inventory = Array.isArray(product.inventory)
            ? product.inventory[0]
            : {};

        delete product.inventory;
        console.log("hit", JSON.stringify(hit));
        return {
            id: product.id,
            name: product.name,
            image: product.image,
            url: product.url,
            sku: product.sku,
            section_id: product.section_id,
            ...inventory, // Merge inventory fields like inventory_id, quantity, price, etc.
        };
    });
};

export const getElasticResultProductTransformResult = (hitsContainer) => {
    const hits = hitsContainer?.hits || [];

    return hits.map((hit) => {
        const product = hit._source || {};
        const inventory =
            hit.inner_hits?.inventory?.hits?.hits?.[0]?._source || {};
        // console.log('product', product)
        return {
            id: product.id,
            name: product.name,
            image: product.image,
            url: product.url,
            sku: product.sku,
            section_id: product.section_id,
            // Flatten inventory details
            ...inventory,
        };
    });
};
