import { CURRENCY } from "../region/config.js";
import { transformProductItem } from "../services/common.service.js";

export const searchProductsELK = async (req) => {
    try {
        const searchTerm = req.query.str?.trim();
        const countryId = req.country_id;
        const currency = CURRENCY[req.country_id].currency;
        if (!searchTerm) {
            return {
                products: [],
                message: "Search string is required",
                statusCode: 400,
                status: "failure",
            };
        }

        const client = await req.server.elasticsearch;

        const esProductResult = await client.search({
            index: process.env.ELASTIC_PRODUCT_INDEX,
            size: 10000,
            body: {
                _source: ["id", "sku", "name", "url", "image"], // Only product fields
                query: {
                    function_score: {
                        query: {
                            bool: {
                                must: [
                                    { term: { status: 1 } },
                                    {
                                        nested: {
                                            path: "inventory",
                                            query: {
                                                bool: {
                                                    must: [
                                                        {
                                                            term: {
                                                                "inventory.country_id":
                                                                    countryId,
                                                            },
                                                        },
                                                        {
                                                            term: {
                                                                "inventory.rstatus": 1,
                                                            },
                                                        },
                                                        {
                                                            match_phrase: {
                                                                "inventory.stock":
                                                                    "In stock",
                                                            },
                                                        },
                                                        {
                                                            range: {
                                                                "inventory.quantity":
                                                                    { gte: 1 },
                                                            },
                                                        },
                                                    ],
                                                },
                                            },
                                            inner_hits: {
                                                size: 1,
                                                _source: {
                                                    includes: [
                                                        "inventory.inventory_id",
                                                        "inventory.country_id",
                                                        "inventory.quantity",
                                                        "inventory.selling_price",
                                                        "inventory.price",
                                                        "inventory.special_price",
                                                        "inventory.promotion_price",
                                                        "inventory.from_date",
                                                        "inventory.to_date",
                                                    ],
                                                },
                                            },
                                        },
                                    },
                                    {
                                        bool: {
                                            should: [
                                                {
                                                    multi_match: {
                                                        query: searchTerm,
                                                        type: "bool_prefix",
                                                        fields: [
                                                            "subcategory_name^4",
                                                            "name",
                                                            "name._2gram",
                                                            "name._3gram",
                                                            "sku",
                                                        ],
                                                    },
                                                },
                                                {
                                                    fuzzy: {
                                                        name: {
                                                            value: searchTerm,
                                                            fuzziness: "AUTO",
                                                        },
                                                    },
                                                },
                                            ],
                                        },
                                    },
                                ],
                            },
                        },
                        functions: [
                            {
                                filter: { match: { subcategory_id: "205" } },
                                weight: 5,
                            },
                            {
                                filter: { match: { subcategory_id: "224" } },
                                weight: 4.9,
                            },
                            {
                                filter: { match: { subcategory_id: "212" } },
                                weight: 4.8,
                            },
                            {
                                filter: { match: { subcategory_id: "206" } },
                                weight: 3,
                            },
                            {
                                filter: { match: { subcategory_id: "455" } },
                                weight: 4.4,
                            },
                        ],
                    },
                },
                aggs: {
                    categories: {
                        terms: { field: "subcategory_id", size: 10000 },
                        aggs: {
                            docs: {
                                top_hits: {
                                    _source: [
                                        "subcategory_id",
                                        "sub_sub_category_id",
                                        "image",
                                        "category_id",
                                        "category_name",
                                        "subcategory_name",
                                        "name",
                                        "sku",
                                        "url",
                                        "sub_sub_category_name",
                                    ],
                                    size: 1,
                                },
                            },
                        },
                    },
                },
            },
        });

        // Redirect check
        // const esRedirectResult = await client.search({
        //     index: "search_redirect2",
        //     size: 1,
        //     _source: true,
        //     body: {
        //         query: {
        //             bool: {
        //                 must: [{ match_phrase: { search_key: searchTerm } }],
        //             },
        //         },
        //     },
        // });

        // Products
        const productsRaw = esProductResult.hits?.hits || [];
        const products = productsRaw.slice(0, 4).map((ele) => {
            const product = ele._source;
            const inventoryHit =
                ele.inner_hits?.inventory?.hits?.hits?.[0]?._source || {};
            return {
                title: product.name,
                type: "detail",
                ...transformProductItem(
                    { ...product, ...inventoryHit },
                    currency
                ),
            };
        });

        // Categories
        const categoryBuckets =
            esProductResult.aggregations?.categories?.buckets || [];
        const categories = categoryBuckets
            .slice(0, 4)
            .map((bucket) => {
                const source = bucket.docs.hits.hits[0]?._source;
                if (!source) return null;

                const {
                    name,
                    subcategory_name,
                    image,
                    subcategory_id,
                    category_id,
                } = source;
                const searchWords = searchTerm.toLowerCase().split(/\s+/);
                const regex = new RegExp(
                    `\\b(?:${searchWords.join("|")}\\w*)\\b(?:\\s+\\w+){0,0}`,
                    "gi"
                );
                const matches = name.match(regex);

                return {
                    title: matches?.[0] || subcategory_name,
                    image: image ? process.env.CDN_IMAGE_BASE_URL + image : "",
                    small_title: matches?.[0] ? subcategory_name : "",
                    subcategory_id,
                    category_id,
                };
            })
            .filter(Boolean);

        // Redirects
        // const redirectHits = esRedirectResult.hits?.hits || [];
        // const output1 = redirectHits.map((hit) => {
        //     const { type, redirect_url, search_key } = hit._source;
        //     let url = "";

        //     if (type === "search_redirect") {
        //         url = new URL(redirect_url).pathname;
        //     } else if (type === "subcategory") {
        //         url = `/products-category/${redirect_url}`;
        //     } else if (type === "brands") {
        //         url = `/brands/${redirect_url}`;
        //     }

        //     return {
        //         title: search_key,
        //         image: "",
        //         small_title: "",
        //         type,
        //         url,
        //     };
        // });

        return {
            // products: [...output1, ...categories, ...products],
            products: [...categories, ...products],
            statusCode: 200,
            status: "success",
        };
    } catch (err) {
        console.error("Elasticsearch Error:", err);
        return {
            products: [],
            message: "Something went wrong",
            statusCode: 500,
            status: "error",
        };
    }
};

export const searchRedirect = async (req) => {
    const client = await req.server.elasticsearch;
    const searchString = req.query?.string || req.body?.searchString;

    if (
        !searchString ||
        typeof searchString !== "string" ||
        !searchString.trim()
    ) {
        throw new Error("Missing or invalid search query string");
    }

    const result = await client.search({
        index: "search_redirect2",
        body: {
            size: 1,
            _source: true,
            query: {
                match_phrase: {
                    search_key: searchString.trim(),
                },
            },
        },
    });

    return result.hits?.hits?.map((h) => h._source) || [];
};

export const searchResultElk = async (req) => {
    const subcategory_id = req.query.subcategory;
    const searchString = req.query.string?.trim();
    const countryId = req.country_id;
    const currency = CURRENCY[countryId].currency;
    if (!searchString) {
        return {
            categories: [],
            brands: [],
            colors: [],
            products: [],
        };
    }

    const client = await req.server.elasticsearch;

    const result = await client.search({
        index: process.env.ELASTIC_PRODUCT_INDEX,
        size: 50,
        _source: [
            // only fetch these fields from product
            "id",
            "sku",
            "name",
            "url",
            "image",
            "subcategory_id",
            "brand_id",
            "color_id",
        ],
        body: {
            query: {
                function_score: {
                    query: {
                        bool: {
                            must: [
                                { term: { status: 1 } },
                                {
                                    nested: {
                                        path: "inventory",
                                        query: {
                                            bool: {
                                                must: [
                                                    {
                                                        term: {
                                                            "inventory.country_id":
                                                                countryId,
                                                        },
                                                    },
                                                    {
                                                        term: {
                                                            "inventory.rstatus": 1,
                                                        },
                                                    },
                                                    {
                                                        match_phrase: {
                                                            "inventory.stock":
                                                                "In stock",
                                                        },
                                                    },
                                                ],
                                            },
                                        },
                                        inner_hits: {
                                            size: 1,
                                            _source: {
                                                includes: [
                                                    "inventory.inventory_id",
                                                    "inventory.country_id",
                                                    "inventory.quantity",
                                                    "inventory.selling_price",
                                                    "inventory.price",
                                                    "inventory.special_price",
                                                    "inventory.promotion_price",
                                                    "inventory.from_date",
                                                    "inventory.to_date",
                                                ],
                                            },
                                        },
                                    },
                                },
                                ...(subcategory_id !== "search"
                                    ? [{ match: { subcategory_id } }]
                                    : []),
                                {
                                    bool: {
                                        should: [
                                            {
                                                multi_match: {
                                                    query: searchString,
                                                    type: "bool_prefix",
                                                    fields: [
                                                        "subcategory_name^4",
                                                        "name",
                                                        "name._2gram",
                                                        "name._3gram",
                                                        "sku",
                                                    ],
                                                },
                                            },
                                            {
                                                fuzzy: {
                                                    name: {
                                                        value: searchString,
                                                        fuzziness: "AUTO",
                                                    },
                                                },
                                            },
                                        ],
                                    },
                                },
                            ],
                        },
                    },
                    functions: [
                        {
                            filter: { match: { subcategory_id: "205" } },
                            weight: 5,
                        },
                        {
                            filter: { match: { subcategory_id: "224" } },
                            weight: 4.9,
                        },
                        {
                            filter: { match: { subcategory_id: "212" } },
                            weight: 4.8,
                        },
                        {
                            filter: { match: { subcategory_id: "63" } },
                            weight: 4.7,
                        },
                        {
                            filter: { match: { subcategory_id: "206" } },
                            weight: 3,
                        },
                        {
                            filter: { match: { subcategory_id: "455" } },
                            weight: 4.4,
                        },
                    ],
                },
            },
            aggs: {
                colors: {
                    terms: { field: "color_id", size: 1000 },
                    aggs: {
                        docs: {
                            top_hits: {
                                _source: ["color_id", "color_name"],
                                size: 1,
                            },
                        },
                    },
                },
                brands: {
                    terms: { field: "brand_id", size: 1000 },
                    aggs: {
                        docs: {
                            top_hits: {
                                _source: ["brand_id", "brand_name"],
                                size: 1,
                            },
                        },
                    },
                },
                categories: {
                    terms: { field: "id", size: 10000 },
                    aggs: {
                        docs: {
                            top_hits: {
                                _source: [
                                    "subcategory_id",
                                    "sub_sub_category_id",
                                    "category_id",
                                    "category_name",
                                    "subcategory_name",
                                    "sub_sub_category_name",
                                ],
                                size: 1,
                            },
                        },
                    },
                },
            },
        },
    });

    const aggregations = result.aggregations || [];
    const hits = result.hits?.hits || [];

    const products = hits.map((h) => {
        const product = h._source;
        const inventory =
            h.inner_hits?.inventory?.hits?.hits?.[0]?._source || {};

        return transformProductItem({ ...product, ...inventory }, currency);
    });

    return {
        categories: aggregations.categories?.buckets || [],
        brands: aggregations.brands?.buckets || [],
        colors: aggregations.colors?.buckets || [],
        products,
    };
};

export const searchResultItemsELK = async (req) => {
    try {
        const countryId = req.country_id;
        const currency = CURRENCY[countryId].currency;
        const subcategory_id = req.query.subcategory;
        const search_elk_redirect = await searchRedirect(req);

        if (search_elk_redirect.length > 0 && subcategory_id === "search") {
            return search_elk_redirect[0];
        }

        const client = await req.server.elasticsearch;

        const result = await client.search({
            index: process.env.ELASTIC_PRODUCT_INDEX,
            size: 50,
            _source: ["id", "sku", "name", "url", "image"],
            body: {
                query: {
                    bool: {
                        must: [
                            { term: { status: 1 } },
                            {
                                nested: {
                                    path: "inventory",
                                    query: {
                                        bool: {
                                            must: [
                                                {
                                                    term: {
                                                        "inventory.country_id":
                                                            countryId,
                                                    },
                                                },
                                                {
                                                    term: {
                                                        "inventory.rstatus": 1,
                                                    },
                                                },
                                                {
                                                    match_phrase: {
                                                        "inventory.stock":
                                                            "In stock",
                                                    },
                                                },
                                            ],
                                        },
                                    },
                                    inner_hits: {
                                        size: 1,
                                        _source: {
                                            includes: [
                                                "inventory.inventory_id",
                                                "inventory.country_id",
                                                "inventory.quantity",
                                                "inventory.selling_price",
                                                "inventory.price",
                                                "inventory.special_price",
                                                "inventory.promotion_price",
                                                "inventory.from_date",
                                                "inventory.to_date",
                                            ],
                                        },
                                    },
                                },
                            },
                        ],
                    },
                },
            },
        });

        const hits = result?.hits?.hits || [];

        const products = hits.map((hit) => {
            const product = hit._source;
            const inventory =
                hit.inner_hits?.inventory?.hits?.hits?.[0]?._source;

            return transformProductItem({ ...product, ...inventory }, currency);
        });

        // Assuming searchResultElk(req) is updated to include the same query and aggregation logic
        const elk_result = await searchResultElk(req);
        const { categories = [], colors = [], brands = [] } = elk_result;

        // Normalize and group categories (same as before)
        const normalizedData = categories
            .map((item) => {
                const src = item?.docs?.hits?.hits?.[0]?._source;
                if (!src) return null;
                return {
                    category_id: src.category_id,
                    value: `${src.category_id}@category`,
                    label: src.category_name,
                    subcategory_id: src.subcategory_id,
                    subcategory_name: src.subcategory_name,
                    sub_sub_category_id: src.sub_sub_category_id,
                    sub_sub_category_name: src.sub_sub_category_name,
                };
            })
            .filter(Boolean);

        const categoryMap = new Map();
        for (const item of normalizedData) {
            if (!categoryMap.has(item.category_id)) {
                categoryMap.set(item.category_id, { ...item, children: [] });
            }
            const category = categoryMap.get(item.category_id);
            let sub = category.children.find(
                (c) => c.subcategory_id === item.subcategory_id
            );
            if (!sub) {
                sub = {
                    category_id: item.category_id,
                    subcategory_id: item.subcategory_id,
                    value: `${item.subcategory_id}_${item.category_id}@subcategory`,
                    label: item.subcategory_name,
                    children: [],
                };
                category.children.push(sub);
            }
            if (item.sub_sub_category_id) {
                sub.children.push({
                    sub_category_id: item.subcategory_id,
                    sub_subcategory_id: item.sub_sub_category_id,
                    value: `${item.sub_sub_category_id}_${item.subcategory_id}@subsubcategory`,
                    label: item.sub_sub_category_name,
                });
            }
        }
        const categoriesOutput = Array.from(categoryMap.values());

        const colorList = colors
            .map((ele) => {
                const src = ele?.docs?.hits?.hits?.[0]?._source;
                return src?.color_id
                    ? { id: src.color_id, name: src.color_name }
                    : null;
            })
            .filter(Boolean);

        const brandList = brands
            .map((ele) => {
                const src = ele?.docs?.hits?.hits?.[0]?._source;
                return src?.brand_id
                    ? { id: src.brand_id, name: src.brand_name }
                    : null;
            })
            .filter(Boolean);

        // Static top brands block (as-is)
        const topBrands = [
            {
                id: 11,
                brand_name: "Samsung",
                url: "Samsung",
                image: "https://cdn.ourshopee.com/ourshopee-img/ourshopee_brands/677654546samsung.png",
            },
            {
                id: 7,
                brand_name: "Apple",
                url: "Apple",
                image: "https://cdn.ourshopee.com/ourshopee-img/ourshopee_brands/26204259310893584apple.png",
            },
            {
                id: 215,
                brand_name: "Xiaomi",
                url: "Xiaomi",
                image: "https://cdn.ourshopee.com/ourshopee-img/ourshopee_brands/674164445xiaomi.png",
            },
            {
                id: 25,
                brand_name: "Oppo",
                url: "Oppo",
                image: "https://cdn.ourshopee.com/ourshopee-img/ourshopee_brands/341608086oppo.png",
            },
            {
                id: 1219,
                brand_name: "Honor",
                url: "Honor",
                image: "https://cdn.ourshopee.com/ourshopee-img/ourshopee_brands/228429790honor.png",
            },
            {
                id: 14,
                brand_name: "Nokia",
                url: "Nokia",
                image: "https://cdn.ourshopee.com/ourshopee-img/ourshopee_brands/378696696nokia.png",
            },
            {
                id: 37,
                brand_name: "Huawei",
                url: "Huawei",
                image: "https://cdn.ourshopee.com/ourshopee-img/ourshopee_brands/946406479huawei.png",
            },
            {
                id: 1037,
                brand_name: "OnePlus",
                url: "OnePlus",
                image: "https://cdn.ourshopee.com/ourshopee-img/ourshopee_brands/711109024Logo-Oneplus.png",
            },
            {
                id: 1608,
                brand_name: "Vivo",
                url: "Vivo",
                image: "https://cdn.ourshopee.com/ourshopee-img/ourshopee_brands/197225333Vivo 01.png",
            },
            {
                id: 2129,
                brand_name: "Realme",
                url: "Realme",
                image: "https://cdn.ourshopee.com/ourshopee-img/ourshopee_brands/339554950realme 01.png",
            },
            {
                id: 1,
                brand_name: "Lenovo",
                url: "Lenovo",
                image: "https://cdn.ourshopee.com/ourshopee-img/ourshopee_brands/108871391lenovo.png",
            },
        ];

        return {
            filters: {
                checkbox: [
                    { id: 1, title: "Colors", list: colorList },
                    { id: 2, title: "Brands", list: brandList },
                ],
                categories: categoriesOutput,
                slider_range: [
                    { title: "price", min_value: 0, max_value: 4000 },
                ],
            },
            display_items: {
                top_brands: topBrands,
                products,
            },
        };
    } catch (err) {
        console.error("search_result_items error:", err.stack);
        return {
            statusCode: 500,
            status: "error",
            message: "Internal Server Error",
        };
    }
};
