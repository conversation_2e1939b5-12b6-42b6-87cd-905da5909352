import jwt from "jsonwebtoken";
import CrmCustomer from "../models/crm_customer.model.js";
import { errorResponse } from "../utils/response.js";

export const verifyJWT = async (request, reply) => {
    try {
        const token =
            request.cookies?.accessToken ||
            request.headers?.authorization?.replace("Bearer ", "");

        if (!token) {
            return errorResponse(
                reply,
                401,
                "Unauthorized request",
                "failure",
                []
            );
        }

        const decodedToken = jwt.verify(token, process.env.TOKEN_SECRET);
        const user = await CrmCustomer.findByPk(decodedToken?.user_id, {
            attributes: [["id", "user_id"], "first_name", "last_name", "email"],
        });

        if (!user) {
            return errorResponse(
                reply,
                401,
                "Invalid access token",
                "failure",
                []
            );
        }

        // request.user = user;
        request.user = user.get({ plain: true });
    } catch (err) {
        return errorResponse(reply, 401, err, [], "error", request);
    }
};
