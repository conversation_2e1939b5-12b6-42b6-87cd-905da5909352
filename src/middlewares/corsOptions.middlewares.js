export const getCorsOptions = () => {
    // Parse allowed origins from env
    const environment = process.env.NODE_ENV;
    const allowedOrigins = process.env.ALLOWED_ORIGINS
        ? process.env.ALLOWED_ORIGINS.split(",").map((origin) => origin.trim())
        : [];

    return {
        origin: (origin, cb) => {
            if (
                !origin ||
                origin === "null" ||
                allowedOrigins.includes(origin)
            ) {
                cb(null, true);
            } else {
                console.warn(`[CORS Blocked] Origin not allowed: ${origin}`);
                cb(new Error("CORS: Origin not allowed"));
            }
        },
        credentials: true, // Allow cookies and Authorization headers
        methods: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
        allowedHeaders: ["Content-Type", "Authorization", "country-id"],
        //   exposedHeaders: [], // Optional
    };
};
