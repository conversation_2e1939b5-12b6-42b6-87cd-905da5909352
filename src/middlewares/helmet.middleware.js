import fp from "fastify-plugin";
import helmet from "@fastify/helmet";

export const getHelmetOptions = () => {
    return {
        contentSecurityPolicy: {
            directives: {
                defaultSrc: ["'self'"],
                styleSrc: [
                    "'self'",
                    "'unsafe-inline'",
                    "https://fonts.googleapis.com",
                ],
                scriptSrc: ["'self'"],
                imgSrc: [
                    "'self'",
                    "data:",
                    "https:",
                    "http:",
                    "*.ourshopee.com",
                ],
                fontSrc: ["'self'", "https://fonts.gstatic.com"],
                connectSrc: [
                    "'self'",
                    "https://api.ourshopee.com",
                    "https://apios.ourshopee.com",
                    "https://www.ourshopee.com",
                    "https://www.os.ourshopee.com",
                ],
                frameSrc: ["'none'"],
                objectSrc: ["'none'"],
                mediaSrc: ["'self'"],
            },
        },

        referrerPolicy: {
            policy: "no-referrer-when-downgrade",
        },

        strictTransportSecurity: {
            maxAge: 31536000,
            includeSubDomains: true,
            preload: true,
        },

        xDnsPrefetchControl: {
            allow: false,
        },

        frameguard: {
            action: "deny",
        },

        hidePoweredBy: true,

        permittedCrossDomainPolicies: {
            permittedPolicies: "none",
        },
    };
};

// Registerable Fastify plugin using the above options
const helmetPlugin = fp(async (fastify) => {
    await fastify.register(helmet, getHelmetOptions());
});

export default helmetPlugin;
