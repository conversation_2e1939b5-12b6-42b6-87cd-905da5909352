// fastify-middleware.js
import multer from "fastify-multer";
import { S3Client, PutObjectCommand } from "@aws-sdk/client-s3";
import "dotenv/config";

// AWS S3 Client Setup
const s3 = new S3Client({
    region: process.env.BUCKET_REGION,
    credentials: {
        accessKeyId: process.env.S3_ACCESS_KEY,
        secretAccessKey: process.env.S3_SECRET_KEY,
    },
});

// Allowed Mimetypes
const allowedMimeTypes = [
    "image/jpeg",
    "image/png",
    "image/gif",
    "image/jpg",
    "image/heic",
    "image/heif",
    "image/webp",
    "image/bmp",
    "image/tiff",
    "image/svg+xml",
    "image/x-icon",
];

const fileFilter = (req, file, cb) => {
    allowedMimeTypes.includes(file.mimetype)
        ? cb(null, true)
        : cb(new Error("Invalid file type. Only images are allowed."), false);
};

const storage = multer.memoryStorage();
const upload = multer({
    storage,
    fileFilter,
    limits: { fileSize: 5 * 1024 * 1024 }, // 5MB
});

// Upload Helper
export async function uploadToS3(
    file,
    directory,
    userId,
    imageNameFromFrontend
) {
    const safeImageName =
        imageNameFromFrontend?.replace(/\s+/g, "_") ||
        file.originalname.replace(/\s+/g, "_");
    const key = `${directory}/${Date.now()}_${userId}_${safeImageName}`;

    const params = {
        Bucket: process.env.BUCKET_NAME,
        Key: key,
        Body: file.buffer,
        ContentType: file.mimetype,
        ACL: "private",
    };

    await s3.send(new PutObjectCommand(params));
    return `${key}`;
}

// Middleware Handler
function handleUpload(path) {
    return async (request, reply) => {
        if (!request.files || !request.files.length) return;

        try {
            const userId = request.user?.user_id || "unknown";
            const imageNames = request.body.imageNames
                ? JSON.parse(request.body.imageNames)
                : {};

            const urls = await Promise.all(
                request.files.map((file, index) =>
                    uploadToS3(
                        file,
                        `ourshopee-img/${path}`, // 👈 use dynamic folder path
                        userId,
                        imageNames[file.fieldname] || file.originalname
                    )
                )
            );

            request.filePaths = urls;
        } catch (err) {
            console.error("💥 Error uploading to S3:", err);
            return reply
                .status(500)
                .send({ message: "Upload failed", error: err.message });
        }
    };
}

// Exported Middleware Factory
export const uploadFile = (fieldName, maxCount = 10, path = "reviews") => [
    upload.array(fieldName, maxCount),
    handleUpload(path),
];
