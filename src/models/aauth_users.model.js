import { DataTypes } from "sequelize";
import sequelize from "../db/sequelize.js";

const AauthUsers = sequelize.define(
    "AauthUsers",
    {
        id: {
            type: DataTypes.INTEGER.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
        },
        client_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        login_source: {
            type: DataTypes.STRING(20),
            allowNull: true,
            defaultValue: "site",
        },
        othersource_uid: {
            type: DataTypes.STRING(100),
            allowNull: true,
        },
        email: {
            type: DataTypes.STRING(100),
            allowNull: true,
        },
        emailverified: {
            type: DataTypes.TINYINT,
            defaultValue: 0,
        },
        mobile: {
            type: DataTypes.STRING(20),
            allowNull: false,
        },
        mobileverified: {
            type: DataTypes.TINYINT,
            defaultValue: 0,
        },
        password: {
            type: DataTypes.STRING(250),
            allowNull: false,
        },
        firstname: {
            type: DataTypes.STRING(100),
            allowNull: true,
        },
        lastname: {
            type: DataTypes.STRING(100),
            allowNull: true,
        },
        username: {
            type: DataTypes.STRING(100),
            allowNull: true,
        },
        profile_pic: {
            type: DataTypes.TEXT,
            allowNull: true,
        },
        rstatus: {
            type: DataTypes.TINYINT,
            defaultValue: 0,
        },
        last_login: {
            type: DataTypes.DATE,
            allowNull: true,
        },
        date_created: {
            type: DataTypes.DATE,
            allowNull: true,
        },
        ip_address: {
            type: DataTypes.TEXT,
            allowNull: true,
        },
        vendor_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        web_token: {
            type: DataTypes.TEXT,
            allowNull: true,
        },
        fcm_token: {
            type: DataTypes.TEXT,
            allowNull: true,
        },
        create_type: {
            type: DataTypes.STRING(20),
            defaultValue: "user",
            comment: "user,staff, vendor,store,etc",
        },
        departmentid: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        category_ids: {
            type: DataTypes.STRING(400),
            allowNull: true,
        },
        agent: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        buyer: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        privilege: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        country_id: {
            type: DataTypes.INTEGER,
            defaultValue: 1,
        },
        os_driver: {
            type: DataTypes.INTEGER,
            defaultValue: 0,
        },
    },
    {
        tableName: "aauth_users",
        timestamps: false,
    }
);

export default AauthUsers;
