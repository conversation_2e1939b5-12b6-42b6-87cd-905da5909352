import { DataTypes } from "sequelize";
import sequelize from "../db/sequelize.js";

const AdminCountryArea = sequelize.define(
    "AdminCountryArea",
    {
        area_id: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            allowNull: false,
            primaryKey: true,
        },
        emirateid: {
            type: DataTypes.STRING(100),
            allowNull: false,
        },
        name: {
            type: DataTypes.STRING(225),
            allowNull: false,
        },
        status: {
            type: DataTypes.INTEGER,
            allowNull: false,
            defaultValue: 1,
        },
    },
    {
        tableName: "admin_country_area",
        timestamps: false,
    }
);

export default AdminCountryArea;
