import { DataTypes } from "sequelize";
import sequelize from "../db/sequelize.js";

const AdminCountryEmirates = sequelize.define(
    "AdminCountryEmirates",
    {
        emirateid: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            allowNull: false,
            primaryKey: true,
        },
        country_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        emirate: {
            type: DataTypes.STRING(200),
            allowNull: false,
        },
    },
    {
        tableName: "admin_country_emirates",
        timestamps: false,
    }
);

export default AdminCountryEmirates;
