import { DataTypes } from "sequelize";
import sequelize from "../db/sequelize.js";

const AdminLists = sequelize.define(
    "admin_lists",
    {
        listid: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
        },
        list_lable: { type: DataTypes.STRING, allowNull: false },
        list_type: { type: DataTypes.STRING, allowNull: false },
        list_key: { type: DataTypes.STRING, allowNull: false },
    },
    {
        tableName: "admin_lists",
        timestamps: false,
    }
);

export default AdminLists;
