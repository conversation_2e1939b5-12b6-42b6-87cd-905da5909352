import { DataTypes } from "sequelize";
import sequelize from "../db/sequelize.js";

const AdminNationality = sequelize.define(
    "AdminNationality",
    {
        id: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            allowNull: false,
            primaryKey: true,
        },
        country_code: {
            type: DataTypes.STRING(2),
            allowNull: false,
            defaultValue: "",
        },
        country_name: {
            type: DataTypes.STRING(100),
            allowNull: false,
            defaultValue: "",
        },
    },
    {
        tableName: "admin_nationalities",
        timestamps: false,
    }
);

export default AdminNationality;
