import { DataTypes } from "sequelize";
import sequelize from "../db/sequelize.js";

const AdminVendor = sequelize.define(
    "AdminVendor",
    {
        vendor_id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
        },
        business_name: {
            type: DataTypes.STRING(255),
            allowNull: true,
        },
        contact_person: {
            type: DataTypes.STRING(50),
            allowNull: true,
        },
        contact_phone: {
            type: DataTypes.STRING(50),
            allowNull: true,
        },
        emaiid: {
            type: DataTypes.STRING(100),
            allowNull: true,
        },
        business_type: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        vatid: {
            type: DataTypes.STRING(100),
            allowNull: true,
        },
        pan: {
            type: DataTypes.STRING(45),
            allowNull: true,
        },
        business_address: {
            type: DataTypes.TEXT("medium"),
            allowNull: true,
        },
        bank_details: {
            type: DataTypes.TEXT("medium"),
            allowNull: true,
        },
        kyc_files: {
            type: DataTypes.TEXT("medium"),
            allowNull: true,
        },
        approved_by: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        approved_on: {
            type: DataTypes.DATE,
            allowNull: true,
            defaultValue: DataTypes.NOW,
        },
        rstatus: {
            type: DataTypes.TINYINT,
            allowNull: true,
            defaultValue: 1,
        },
    },
    {
        tableName: "admin_vendor",
        timestamps: false,
    }
);

export default AdminVendor;
