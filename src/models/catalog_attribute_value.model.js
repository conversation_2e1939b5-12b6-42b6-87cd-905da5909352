import { DataTypes } from "sequelize";
import sequelize from "../db/sequelize.js";

const CatalogAttributeValue = sequelize.define(
    "CatalogAttributeValue",
    {
        attributevalueid: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
        },
        attributeid: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        name: {
            type: DataTypes.STRING,
        },
        slug: {
            type: DataTypes.STRING,
        },
    },
    {
        tableName: "catalog_attribute_values",
        timestamps: false,
    }
);

export default CatalogAttributeValue;
