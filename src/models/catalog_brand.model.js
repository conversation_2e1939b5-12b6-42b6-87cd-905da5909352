import { DataTypes } from "sequelize";
import sequelize from "../db/sequelize.js";

const CatalogBrand = sequelize.define(
    "CatalogBrand",
    {
        brandid: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            primaryKey: true,
        },
        country_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        brand_name: {
            type: DataTypes.STRING(275),
            allowNull: false,
        },
        arabic_name: {
            type: DataTypes.STRING(200),
            allowNull: true,
        },
        slug: {
            type: DataTypes.STRING(45),
            allowNull: false,
        },
        brand_images: {
            type: DataTypes.TEXT,
            allowNull: true,
        },
        seo_title: {
            type: DataTypes.STRING(250),
            allowNull: true,
        },
        seo_description: {
            type: DataTypes.TEXT,
            allowNull: true,
        },
        seo_keywords: {
            type: DataTypes.TEXT,
            allowNull: true,
        },
        updated_by: {
            type: DataTypes.STRING(45),
            allowNull: true,
        },
        updated_date: {
            type: DataTypes.DATE,
            allowNull: true,
            defaultValue: DataTypes.NOW,
        },
        featured: {
            type: DataTypes.TINYINT,
            allowNull: true,
        },
        home_page: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        rstatus: {
            type: DataTypes.TINYINT,
            allowNull: true,
            defaultValue: 1,
        },
    },
    {
        tableName: "catalog_brand",
        timestamps: false,
        indexes: [
            {
                fields: ["brand_name"],
            },
            {
                fields: ["slug"],
            },
        ],
    }
);

export default CatalogBrand;
