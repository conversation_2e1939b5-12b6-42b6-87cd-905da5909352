// models/catalog_category.model.js
import { DataTypes } from "sequelize";
import sequelize from "../db/sequelize.js";

const CatalogCategory = sequelize.define(
    "CatalogCategory",
    {
        categoryid: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            primaryKey: true,
        },
        categorypid: {
            type: DataTypes.INTEGER,
            defaultValue: 0,
        },
        country_id: {
            type: DataTypes.INTEGER,
            defaultValue: 0,
        },
        client_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        category_type: {
            type: DataTypes.TINYINT,
            defaultValue: 1,
            comment: "1 - Products, 2 - Post",
        },
        attribute_ids: {
            type: DataTypes.TEXT,
            allowNull: true,
        },
        category_name: {
            type: DataTypes.STRING(250),
            allowNull: true,
        },
        arabic_name: {
            type: DataTypes.STRING(250),
            allowNull: true,
        },
        slug: {
            type: DataTypes.STRING(250),
            allowNull: true,
        },
        category_images: {
            type: DataTypes.TEXT("medium"),
            allowNull: true,
        },
        slider_images: {
            type: DataTypes.TEXT("medium"),
            allowNull: true,
        },
        seo_title: {
            type: DataTypes.STRING(255),
            allowNull: true,
        },
        seo_description: {
            type: DataTypes.STRING(255),
            allowNull: true,
        },
        seo_keywords: {
            type: DataTypes.STRING(255),
            allowNull: true,
        },
        description: {
            type: DataTypes.STRING(500),
            allowNull: true,
        },
        arabic_description: {
            type: DataTypes.STRING(500),
            allowNull: true,
        },
        updated_date: {
            type: DataTypes.DATE,
            allowNull: true,
            defaultValue: DataTypes.NOW,
        },
        rstatus: {
            type: DataTypes.TINYINT,
            defaultValue: 1,
        },
        position: {
            type: DataTypes.INTEGER,
            defaultValue: 1,
        },
        cms_category_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        os_category_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        front_view: {
            type: DataTypes.INTEGER,
            defaultValue: 0,
        },
        home_page: {
            type: DataTypes.INTEGER,
            defaultValue: 0,
        },
        other_setting: {
            type: DataTypes.TEXT("medium"),
            allowNull: true,
        },
        tabby_type: {
            type: DataTypes.INTEGER,
            defaultValue: 0,
        },
    },
    {
        tableName: "catalog_category",
        timestamps: false,
    }
);

// Self-referencing association (must be passed `models`)
CatalogCategory.associate = (models) => {
    CatalogCategory.hasMany(models.CatalogCategory, {
        foreignKey: "categorypid",
        as: "subcategories",
    });

    CatalogCategory.hasMany(models.CatalogCategory, {
        foreignKey: "categorypid",
        as: "sub_subcategories",
    });

    CatalogCategory.belongsTo(models.CatalogCategory, {
        foreignKey: "categorypid",
        as: "parentCategory",
    });
};

export default CatalogCategory;
