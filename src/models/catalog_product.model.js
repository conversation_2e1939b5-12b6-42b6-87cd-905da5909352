import { DataTypes } from "sequelize";
import sequelize from "../db/sequelize.js";

const CatalogProduct = sequelize.define(
    "CatalogProduct",
    {
        productid: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            primaryKey: true,
        },
        product_condition: { type: DataTypes.STRING(45), allowNull: true },
        type_id: {
            type: DataTypes.TINYINT,
            defaultValue: 1,
            comment: "1: Single Product, 2: Bundle Product",
        },
        vendor_id: { type: DataTypes.INTEGER, defaultValue: 0 },
        categoryid: { type: DataTypes.INTEGER, defaultValue: 0 },
        brandid: { type: DataTypes.INTEGER, defaultValue: 0 },
        model_no: { type: DataTypes.STRING(100), allowNull: true },
        sku: { type: DataTypes.STRING(255), allowNull: true },
        slug: { type: DataTypes.STRING(255), allowNull: true },
        title: { type: DataTypes.STRING(255), allowNull: true },
        arabic_title: { type: DataTypes.TEXT, allowNull: true },
        short_description: { type: DataTypes.STRING(255), allowNull: true },
        arabic_short_description: {
            type: DataTypes.STRING(45),
            allowNull: true,
        },
        description: { type: DataTypes.TEXT, allowNull: true },
        arabic_description: { type: DataTypes.TEXT, allowNull: true },
        specifications: { type: DataTypes.TEXT, allowNull: true },
        arabic_specifications: { type: DataTypes.TEXT, allowNull: true },
        dimensions: { type: DataTypes.STRING(255), allowNull: true },
        product_images: { type: DataTypes.TEXT, allowNull: true },
        seo_title: { type: DataTypes.STRING(50), allowNull: true },
        seo_description: { type: DataTypes.STRING(500), allowNull: true },
        seo_keyword: { type: DataTypes.STRING(255), allowNull: true },
        featured_flag: { type: DataTypes.TINYINT, defaultValue: 0 },
        variant_flag: { type: DataTypes.TINYINT, defaultValue: 0 },
        variantname: { type: DataTypes.STRING(80), allowNull: true },
        variant_code: { type: DataTypes.STRING(255), allowNull: true },
        bundle_ids: { type: DataTypes.TEXT("medium"), allowNull: true },
        return_flag: { type: DataTypes.TINYINT, defaultValue: 1 },
        video_link: { type: DataTypes.STRING(255), allowNull: true },
        vendor_productid: { type: DataTypes.INTEGER, defaultValue: 0 },
        approved_by: { type: DataTypes.INTEGER, allowNull: true },
        created_date: { type: DataTypes.DATE, defaultValue: DataTypes.NOW },
        created_by: { type: DataTypes.INTEGER, allowNull: true },
        updated_date: { type: DataTypes.DATE, defaultValue: DataTypes.NOW },
        updated_by: { type: DataTypes.INTEGER, allowNull: true },
        rstatus: { type: DataTypes.TINYINT, defaultValue: 1 },
        buyer_id: { type: DataTypes.INTEGER, allowNull: true },
        dealer_id: { type: DataTypes.INTEGER, defaultValue: 0 },
        os_product_id: { type: DataTypes.INTEGER, allowNull: true },
        os_buyer_id: { type: DataTypes.INTEGER, allowNull: true },
        warranty: { type: DataTypes.STRING(45), allowNull: true },
        warranty_type: { type: DataTypes.INTEGER, allowNull: true },
        os_brands_category_id: { type: DataTypes.INTEGER, allowNull: true },
        midnight: { type: DataTypes.INTEGER, allowNull: true },
        most_selling: { type: DataTypes.INTEGER, allowNull: true },
        credit_card: { type: DataTypes.INTEGER, allowNull: true },
        hotdeal: { type: DataTypes.INTEGER, allowNull: true },
        views: { type: DataTypes.INTEGER, allowNull: true },
        deal: { type: DataTypes.INTEGER, allowNull: true },
        weight: { type: DataTypes.FLOAT, allowNull: true },
        length: { type: DataTypes.FLOAT, allowNull: true },
        width: { type: DataTypes.FLOAT, allowNull: true },
        height: { type: DataTypes.FLOAT, allowNull: true },
    },
    {
        tableName: "catalog_product",
        timestamps: false,
        indexes: [{ fields: ["title"] }, { fields: ["sku"] }],
    }
);

CatalogProduct.associate = (models) => {
    CatalogProduct.hasOne(models.CatalogProductInventory, {
        foreignKey: "productid",
        sourceKey: "productid",
        as: "inventory",
    });

    CatalogProduct.belongsTo(models.CatalogCategory, {
        foreignKey: "categoryid", // use correct FK here
        targetKey: "categoryid",
        as: "subcategory",
    });

    CatalogProduct.belongsTo(models.CatalogBrand, {
        foreignKey: "brandid",
        targetKey: "brandid",
        as: "brands",
    });
};

export default CatalogProduct;
