import { DataTypes } from "sequelize";
import sequelize from "../db/sequelize.js";
import CatalogProduct from "./catalog_product.model.js";

const CatalogProductInventory = sequelize.define(
    "CatalogProductInventory",
    {
        inventory_id: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            primaryKey: true,
        },
        productid: {
            type: DataTypes.INTEGER,
            primaryKey: true,
        },
        country_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        vendor_id: {
            type: DataTypes.INTEGER,
            defaultValue: 0,
        },
        variant_attributevalues: {
            type: DataTypes.TEXT("medium"),
            allowNull: true,
        },
        variantid: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        tax: {
            type: DataTypes.FLOAT,
            allowNull: true,
        },
        cost: {
            type: DataTypes.FLOAT,
            allowNull: true,
        },
        mrp: {
            type: DataTypes.FLOAT,
            defaultValue: 0,
        },
        selling_price: {
            type: DataTypes.FLOAT,
            defaultValue: 0,
        },
        promotion_price: {
            type: DataTypes.FLOAT,
            allowNull: true,
        },
        inventory: {
            type: DataTypes.INTEGER,
            defaultValue: 0,
        },
        mol: {
            type: DataTypes.INTEGER,
            defaultValue: 0,
            comment: "Minimum Order level for next purchase alert",
        },
        stock_status: {
            type: DataTypes.TINYINT,
            defaultValue: 0,
        },
        updated_by: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        updated_date: {
            type: DataTypes.DATE,
            defaultValue: DataTypes.NOW,
        },
        rstatus: {
            type: DataTypes.TINYINT,
            defaultValue: 1,
        },
        shipping_charge: {
            type: DataTypes.FLOAT,
            allowNull: true,
        },
        shipping_days: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        rate: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        promotion_from_date: {
            type: DataTypes.DATE,
            allowNull: true,
        },
        promotion_to_date: {
            type: DataTypes.DATE,
            allowNull: true,
        },
        offer_from: {
            type: DataTypes.DATE,
            allowNull: true,
        },
        offer_to: {
            type: DataTypes.DATE,
            allowNull: true,
        },
        max_pur_qty: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        exiting_offer: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        return_flag: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
    },
    {
        tableName: "catalog_product_inventory",
        timestamps: false,
        indexes: [
            { fields: ["productid"] },
            { fields: ["vendor_id"] },
            { fields: ["country_id"] },
        ],
    }
);

// CatalogProductInventory.associate = () => {
//     CatalogProductInventory.belongsTo(CatalogProduct, {
//         foreignKey: "productid",
//         targetKey: "productid",
//         as: "product",
//     });
// };

CatalogProductInventory.associate = (models) => {
    CatalogProductInventory.belongsTo(models.CatalogProduct, {
        foreignKey: "productid",
        targetKey: "productid",
        as: "product",
    });
};

export default CatalogProductInventory;
