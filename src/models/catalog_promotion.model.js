import { DataTypes } from "sequelize";
import sequelize from "../db/sequelize.js";

const CatalogPromotion = sequelize.define(
    "CatalogPromotion",
    {
        promotionid: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
        },
        channel_type: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        promo_type: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        promo_code: {
            type: DataTypes.STRING(45),
            allowNull: true,
        },
        max_usa_user: {
            type: DataTypes.INTEGER,
            defaultValue: 1,
        },
        max_usa_platform: {
            type: DataTypes.INTEGER,
            defaultValue: 0,
        },
        promo_discription: {
            type: DataTypes.STRING(255),
            allowNull: true,
        },
        min_order_value: {
            type: DataTypes.FLOAT,
            allowNull: true,
        },
        deduction_type: {
            type: DataTypes.STRING(10),
            defaultValue: "percent",
            comment: "Percentage or fixed",
        },
        deduction_percent: {
            type: DataTypes.INTEGER,
            defaultValue: 0,
        },
        deduction_amount: {
            type: DataTypes.FLOAT,
            allowNull: true,
        },
        startdate: {
            type: DataTypes.DATE,
            allowNull: true,
        },
        enddate: {
            type: DataTypes.DATE,
            allowNull: true,
        },
        stacking_flag: {
            type: DataTypes.INTEGER,
            allowNull: true,
            comment: "Number of times used",
        },
        productid: {
            type: DataTypes.STRING(500),
            allowNull: true,
        },
        brandid: {
            type: DataTypes.STRING(100),
            allowNull: true,
        },
        categoryid: {
            type: DataTypes.STRING(100),
            allowNull: true,
        },
        customerid: {
            type: DataTypes.STRING(100),
            allowNull: true,
        },
        updated_date: {
            type: DataTypes.DATE,
            allowNull: true,
        },
        updated_by: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        rstatus: {
            type: DataTypes.INTEGER,
            defaultValue: 1,
        },
    },
    {
        tableName: "catalog_promotion",
        timestamps: false,
    }
);

export default CatalogPromotion;
