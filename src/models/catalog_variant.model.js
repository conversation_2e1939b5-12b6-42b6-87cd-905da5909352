import { DataTypes } from "sequelize";
import sequelize from "../db/sequelize.js";

const CatalogVariant = sequelize.define(
    "CatalogVariant",
    {
        variantid: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
        },
        variantgroupid: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        attributevalues_ids: {
            type: DataTypes.STRING, // Storing comma-separated IDs
            allowNull: false,
        },
        variant_code: {
            type: DataTypes.STRING,
        },
    },
    {
        tableName: "catalog_variant",
        timestamps: false,
    }
);

export default CatalogVariant;
