import { DataTypes } from "sequelize";
import sequelize from "../db/sequelize.js";

const CmsBanners = sequelize.define(
    "CmsBanners",
    {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
        },
        country_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
            defaultValue: 1,
        },
        type: {
            type: DataTypes.STRING(45),
            allowNull: true,
            defaultValue: "home_page",
            comment: `1-Home Slider -  ourshopee_image_sliders
                    2-Home Banner - ourshopee_homebanners
                    3-Other Slider - ourshopee_all_sliders
                    4-Other Banner - ourshopee_banners
                    5-Section - ourshopee_section_images
                    6-Category Slider - ourshopee_category_sliders
                    7-Brand`,
        },
        type_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        rstatus: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        updated_date: {
            type: DataTypes.DATE,
            allowNull: true,
            defaultValue: DataTypes.NOW,
        },
        ubdated_by: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        position: {
            type: DataTypes.INTEGER,
            allowNull: true,
            defaultValue: 1,
        },
        banner_images: {
            type: DataTypes.TEXT("long"),
            allowNull: true,
        },
        brandid: {
            type: DataTypes.INTEGER,
            allowNull: true,
            defaultValue: 0,
        },
        productid: {
            type: DataTypes.INTEGER,
            allowNull: true,
            defaultValue: 0,
        },
    },
    {
        tableName: "cms_banners",
        timestamps: false,
    }
);

CmsBanners.associate = (models) => {
    CmsBanners.belongsTo(models.CatalogBrand, {
        foreignKey: "brandid",
        targetKey: "brandid",
        as: "brand",
    });
};

export default CmsBanners;
