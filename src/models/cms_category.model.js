import { DataTypes } from "sequelize";
import sequelize from "../db/sequelize.js"; // adjust path if needed


const getCMSCategoryModel = (countryId) => {
    const tableMap = {
        2: "oman_cms_category",
        3: "qatar_cms_category",
        5: "kuwait_cms_category",
        6: "qatar_cms_category",
    };

    const tableName = tableMap?.[countryId] || "ourshopee_category";

    return sequelize.define("ourshopee_category", {
        id: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            allowNull: false,
            primaryKey: true,
        },
        name: {
            type: DataTypes.STRING(275),
            allowNull: false,
        },
        name_arabic: {
            type: DataTypes.STRING(150),
            allowNull: false,
        },
        position: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        url: {
            type: DataTypes.TEXT,
            allowNull: false,
        },
        image: {
            type: DataTypes.STRING(225),
            allowNull: false,
        },
        icon: {
            type: DataTypes.STRING(225),
            allowNull: false,
        },
        banner_image: {
            type: DataTypes.STRING(225),
            allowNull: false,
        },
        mobile_banner_image: {
            type: DataTypes.STRING(225),
            allowNull: false,
        },
        seo_title: {
            type: DataTypes.TEXT,
            allowNull: false,
        },
        seo_description: {
            type: DataTypes.TEXT,
            allowNull: false,
        },
        seo_keywords: {
            type: DataTypes.TEXT,
            allowNull: false,
        },
        description: {
            type: DataTypes.TEXT,
            allowNull: false,
        },
        app_icon: {
            type: DataTypes.STRING(225),
            allowNull: false,
        },
        mobile_icon: {
            type: DataTypes.STRING(225),
            allowNull: false,
        },
        vector_icon: {
            type: DataTypes.STRING(225),
            allowNull: false,
        },
        color_code: {
            type: DataTypes.STRING(10),
            allowNull: false,
        },
        txtcolor_code: {
            type: DataTypes.STRING(10),
            allowNull: false,
        },
        bdcolor_code: {
            type: DataTypes.STRING(10),
            allowNull: false,
        },
        status: {
            type: DataTypes.TINYINT,
            allowNull: false,
            defaultValue: 1,
        },
        pixel_status: {
            type: DataTypes.STRING(15),
            allowNull: false,
        },
        front_view: {
            type: DataTypes.TINYINT,
            allowNull: false,
        },
        schema_details: {
            type: DataTypes.TEXT,
            allowNull: false,
        },
        cms_category_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        outer_class: {
            type: DataTypes.TEXT,
            allowNull: false,
        },
        home_page: {
            type: DataTypes.TINYINT,
            allowNull: false,
        },
    }, {
        tableName,
        timestamps: false,
        indexes: [
            {
                name: "PRIMARY",
                unique: true,
                using: "BTREE",
                fields: ["id"],
            },
            {
                name: "idx__id",
                using: "BTREE",
                fields: ["id"],
            },
            {
                name: "idx__name",
                using: "BTREE",
                fields: ["name"],
            },
        ],
    });
}

export default getCMSCategoryModel;
