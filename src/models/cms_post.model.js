import { DataTypes } from "sequelize";
import sequelize from "../db/sequelize.js";

const CmsPost = sequelize.define(
    "CmsPost",
    {
        postid: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            primaryKey: true,
        },
        posts_type: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        categoryid: {
            type: DataTypes.STRING(255),
            allowNull: true,
        },
        slug: {
            type: DataTypes.STRING(255),
            allowNull: true,
        },
        title: {
            type: DataTypes.STRING(255),
            allowNull: true,
        },
        post_body: {
            type: DataTypes.TEXT("long"),
            allowNull: true,
        },
        short_description: {
            type: DataTypes.TEXT("long"),
            allowNull: true,
        },
        post_images: {
            type: DataTypes.TEXT("long"),
            allowNull: true,
        },
        video_link: {
            type: DataTypes.STRING(255),
            allowNull: true,
        },
        tags: {
            type: DataTypes.TEXT,
            allowNull: true,
        },
        display_order: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        featured: {
            type: DataTypes.INTEGER,
            defaultValue: 0,
        },
        authorname: {
            type: DataTypes.STRING(250),
            allowNull: false,
        },
        post_status: {
            type: DataTypes.TINYINT,
            defaultValue: 1,
        },
        created_by: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        published_on: {
            type: DataTypes.DATE,
            allowNull: true,
        },
        updated_date: {
            type: DataTypes.DATE,
            defaultValue: DataTypes.NOW,
        },
    },
    {
        tableName: "cms_post",
        timestamps: false,
    }
);

export default CmsPost;
