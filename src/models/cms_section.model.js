import { DataTypes } from "sequelize";
import sequelize from "../db/sequelize.js";

const CmsSection = sequelize.define(
    "CmsSection",
    {
        id: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            primaryKey: true,
        },
        country_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        seo_title: {
            type: DataTypes.STRING(120),
            allowNull: false,
        },
        seo_description: {
            type: DataTypes.STRING(220),
            allowNull: false,
        },
        seo_keywords: {
            type: DataTypes.STRING(220),
            allowNull: false,
        },
        name: {
            type: DataTypes.STRING(275),
            allowNull: false,
        },
        category_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        subcategory_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        name_arabic: {
            type: DataTypes.STRING(150),
            allowNull: false,
        },
        position: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        url: {
            type: DataTypes.TEXT,
            allowNull: false,
        },
        outer_css: {
            type: DataTypes.TEXT,
            allowNull: false,
        },
        outer_css_arabic: {
            type: DataTypes.TEXT,
            allowNull: false,
        },
        css: {
            type: DataTypes.STRING(30),
            allowNull: false,
        },
        link: {
            type: DataTypes.TEXT,
            allowNull: false,
        },
        mobile_link: {
            type: DataTypes.TEXT,
            allowNull: false,
        },
        arabic_link: {
            type: DataTypes.TEXT,
            allowNull: false,
        },
        arabicmobile_link: {
            type: DataTypes.TEXT,
            allowNull: false,
        },
        arabic_image: {
            type: DataTypes.STRING(225),
            allowNull: false,
        },
        image: {
            type: DataTypes.STRING(225),
            allowNull: false,
        },
        description: {
            type: DataTypes.TEXT,
            allowNull: false,
        },
        from_date: {
            type: DataTypes.DATE,
            allowNull: false,
        },
        to_date: {
            type: DataTypes.DATE,
            allowNull: false,
        },
        midnight: {
            type: DataTypes.TINYINT,
            allowNull: false,
        },
        app_icon: {
            type: DataTypes.STRING(225),
            allowNull: false,
        },
        front_view: {
            type: DataTypes.TINYINT,
            allowNull: false,
        },
        status: {
            type: DataTypes.TINYINT,
            allowNull: false,
            defaultValue: 1,
        },
        fashion_status: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        title_image: {
            type: DataTypes.STRING(225),
            allowNull: false,
        },
        deal_type: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        bg_css: {
            type: DataTypes.STRING(100),
            allowNull: false,
        },
        dynamic_page: {
            type: DataTypes.INTEGER,
            allowNull: false,
            defaultValue: 0,
        },
        filter_deal: {
            type: DataTypes.INTEGER,
            allowNull: false,
            defaultValue: 0,
        },
        section_images: {
            type: DataTypes.TEXT,
            allowNull: true,
        },
    },
    {
        tableName: "cms_section",
        timestamps: false,
    }
);

export default CmsSection;
