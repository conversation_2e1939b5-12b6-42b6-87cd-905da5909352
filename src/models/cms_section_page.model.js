import { DataTypes } from "sequelize";
import sequelize from "../db/sequelize.js";

const CmsSectionPage = sequelize.define(
    "CmsSectionPage",
    {
        id: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            primaryKey: true,
        },
        section_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        title_en: {
            type: DataTypes.TEXT,
            allowNull: false,
        },
        title_ar: {
            type: DataTypes.TEXT,
            allowNull: false,
        },
        html_type: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        outer_css: {
            type: DataTypes.TEXT,
            allowNull: false,
        },
        main_css: {
            type: DataTypes.STRING(225),
            allowNull: false,
        },
        list_css: {
            type: DataTypes.STRING(225),
            allowNull: false,
        },
        title_display: {
            type: DataTypes.STRING(25),
            allowNull: false,
        },
        bg_format: {
            type: DataTypes.STRING(225),
            allowNull: false,
        },
        text_format: {
            type: DataTypes.STRING(225),
            allowNull: false,
        },
        position: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        status: {
            type: DataTypes.TINYINT,
            allowNull: false,
            defaultValue: 1,
        },
        updated_by: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        updated_date: {
            type: DataTypes.DATE,
            allowNull: false,
        },
    },
    {
        tableName: "cms_section_page",
        timestamps: false,
    }
);

// ✅ Define hasMany relationship to CmsSectionPageDetails
CmsSectionPage.associate = (models) => {
    CmsSectionPage.hasMany(models.CmsSectionPageDetails, {
        foreignKey: "page_id",
        as: "details",
    });
};

export default CmsSectionPage;
