import { DataTypes } from "sequelize";
import sequelize from "../db/sequelize.js";

const CmsSectionPageDetails = sequelize.define(
    "CmsSectionPageDetails",
    {
        id: {
            type: DataTypes.INTEGER.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
        },
        page_id: {
            type: DataTypes.INTEGER.UNSIGNED,
            allowNull: false,
        },
        title_en: {
            type: DataTypes.TEXT,
            allowNull: false,
        },
        title_ar: {
            type: DataTypes.TEXT,
            allowNull: false,
        },
        en_image: {
            type: DataTypes.STRING(275),
            allowNull: false,
        },
        ar_image: {
            type: DataTypes.STRING(275),
            allowNull: false,
        },
        en_mobile_image: {
            type: DataTypes.STRING(275),
            allowNull: false,
        },
        ar_mobile_image: {
            type: DataTypes.STRING(275),
            allowNull: false,
        },
        subcategory_id: {
            type: DataTypes.INTEGER.UNSIGNED,
            allowNull: false,
        },
        tag_id: {
            type: DataTypes.STRING(50),
            allowNull: false,
        },
        tag_name: {
            type: DataTypes.INTEGER.UNSIGNED,
            allowNull: false,
        },
        position: {
            type: DataTypes.INTEGER.UNSIGNED,
            allowNull: false,
        },
        status: {
            type: DataTypes.BOOLEAN,
            defaultValue: true,
        },
        sub_sub_cat_id: {
            type: DataTypes.INTEGER.UNSIGNED,
            allowNull: false,
        },
        brand_id: {
            type: DataTypes.INTEGER.UNSIGNED,
            allowNull: false,
        },
        en_redirection: {
            type: DataTypes.STRING(225),
            allowNull: false,
        },
        ar_redirection: {
            type: DataTypes.STRING(225),
            allowNull: false,
        },
        video: {
            type: DataTypes.STRING(225),
            allowNull: false,
        },
    },
    {
        tableName: "cms_section_page_details",
        timestamps: false,
    }
);

// // 👇 Association declared via a method
// CmsSectionPageDetails.associate = () => {
//     CmsSectionPageDetails.belongsTo(CmsSectionPage, {
//         foreignKey: "page_id",
//         targetKey: "id",
//         as: "sectionPage",
//     });

//     CmsSectionPageDetails.belongsTo(OurshopeeSubcategory, {
//         foreignKey: "subcategory_id",
//         targetKey: "id",
//         as: "subcategory",
//     });

//     CmsSectionPageDetails.belongsTo(OurshopeeSubsubcategory, {
//         foreignKey: "sub_sub_cat_id",
//         targetKey: "id",
//         as: "subsubcategory",
//     });

//     CmsSectionPageDetails.belongsTo(OurshopeeBrand, {
//         foreignKey: "brand_id",
//         targetKey: "brandid",
//         as: "brand",
//     });
// };

CmsSectionPageDetails.associate = (models) => {
    CmsSectionPageDetails.belongsTo(models.CmsSectionPage, {
        foreignKey: "page_id",
        as: "sectionPage",
    });

    CmsSectionPageDetails.belongsTo(models.CatalogCategory, {
        foreignKey: "subcategory_id",
        as: "subcategory",
    });

    CmsSectionPageDetails.belongsTo(models.CatalogCategory, {
        foreignKey: "subcategory_id",
        as: "subsubcategory",
    });

    CmsSectionPageDetails.belongsTo(models.CatalogCategory, {
        foreignKey: "sub_sub_cat_id",
        as: "sub_subcategory",
    });

    CmsSectionPageDetails.belongsTo(models.CatalogBrand, {
        foreignKey: "brand_id",
        as: "brand",
    });
};

export default CmsSectionPageDetails;
