import { DataTypes } from "sequelize";
import sequelize from "../db/sequelize.js"; // Adjust the path if needed

const getCMSSubSubCategoryModel = (countryId) => {
  const tableMap = {
    2: "oman_cms_subsubcategory",
    3: "qatar_cms_subsubcategory",
    5: "kuwait_cms_subsubcategory",
    6: "bahrain_cms_subsubcategory",
  };

  const tableName = tableMap?.[countryId] || "ourshopee_subsubcategory";

  return sequelize.define("cms_subsubcategory", {
    id: {
      autoIncrement: true,
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true,
    },
    category_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    subcategory_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    name: {
      type: DataTypes.STRING(275),
      allowNull: false,
      unique: "name",
    },
    name_arabic: {
      type: DataTypes.STRING(150),
      allowNull: false,
    },
    position: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    url: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    link: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    mobile_link: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    seo_title: {
      type: DataTypes.STRING(80),
      allowNull: false,
    },
    seo_description: {
      type: DataTypes.STRING(210),
      allowNull: false,
    },
    seo_keywords: {
      type: DataTypes.STRING(210),
      allowNull: false,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    image: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    app_icon: {
      type: DataTypes.STRING(225),
      allowNull: false,
    },
    arabic_image: {
      type: DataTypes.STRING(225),
      allowNull: false,
    },
    arabic_app_icon: {
      type: DataTypes.STRING(225),
      allowNull: false,
    },
    first_parameter: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    last_parameter: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    front_view: {
      type: DataTypes.TINYINT,
      allowNull: false,
    },
  }, {
    tableName,
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: ["id"],
      },
      {
        name: "name",
        unique: true,
        using: "BTREE",
        fields: ["name"],
      },
    ],
  });
};

export default getCMSSubSubCategoryModel;
