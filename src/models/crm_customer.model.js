import { DataTypes } from "sequelize";
import sequelize from "../db/sequelize.js";
import bcrypt from "bcryptjs";

const CrmCustomer = sequelize.define(
    "CrmCustomer",
    {
        id: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            primaryKey: true,
        },
        country_id: {
            type: DataTypes.INTEGER,
            defaultValue: 1,
        },
        first_name: {
            type: DataTypes.STRING(225),
            allowNull: true,
        },
        last_name: {
            type: DataTypes.STRING(225),
            allowNull: true,
        },
        email: {
            type: DataTypes.STRING(80),
            allowNull: true,
            unique: true,
        },
        gender: {
            type: DataTypes.STRING(225),
            allowNull: true,
        },
        nationality: {
            type: DataTypes.STRING(225),
            allowNull: true,
        },
        password: {
            type: DataTypes.STRING(255),
            allowNull: true,
        },
        mobile: {
            type: DataTypes.STRING(15),
            allowNull: true,
        },
        date: {
            type: DataTypes.DATE,
            allowNull: true,
        },
        refid: {
            type: DataTypes.STRING(100),
            allowNull: true,
        },
        country: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        emirates: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        promotion_code: {
            type: DataTypes.STRING(100),
            allowNull: true,
            comment:
                "To check if a promotion code is associated with the user's registration.",
        },
        total_points: {
            type: DataTypes.STRING(200),
            allowNull: true,
        },
        rstatus: {
            type: DataTypes.TINYINT,
            allowNull: true,
        },
        email_status: {
            type: DataTypes.TINYINT,
            allowNull: true,
        },
        default_address: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        device_token: {
            type: DataTypes.TEXT("long"),
            allowNull: true,
        },
        device_type: {
            type: DataTypes.STRING(225),
            allowNull: true,
        },
        vip: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        vip_expiry_date: {
            type: DataTypes.DATE,
            allowNull: true,
        },
        dob: {
            type: DataTypes.DATEONLY,
            allowNull: true,
        },
        platform: {
            type: DataTypes.STRING(10),
            allowNull: true,
        },
        guest_checkout: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        user_ip_address: {
            type: DataTypes.TEXT,
            allowNull: true,
        },
        token: {
            type: DataTypes.STRING(5),
            allowNull: true,
        },
    },
    {
        tableName: "crm_customer",
        timestamps: false,
        hooks: {
            beforeCreate: async (user) => {
                if (user.password) {
                    const salt = await bcrypt.genSalt(10);
                    user.password = await bcrypt.hash(user.password, salt);
                }
            },

            beforeUpdate: async (user) => {
                if (user.changed("password")) {
                    const salt = await bcrypt.genSalt(10);
                    user.password = await bcrypt.hash(user.password, salt);
                }
            },
        },
    }
);

// Instance method to compare password
CrmCustomer.prototype.comparePassword = async function (candidatePassword) {
    return await bcrypt.compare(candidatePassword, this.password);
};

export default CrmCustomer;
