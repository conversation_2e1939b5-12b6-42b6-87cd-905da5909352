import { DataTypes } from "sequelize";
import sequelize from "../db/sequelize.js";

const CrmCustomerAddress = sequelize.define(
    "CrmCustomerAddress",
    {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
        },
        country_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        customer_id: {
            type: DataTypes.BIGINT,
            allowNull: false,
        },
        name: {
            type: DataTypes.STRING(225),
            allowNull: true,
        },
        last_name: {
            type: DataTypes.STRING(225),
            allowNull: true,
        },
        company: {
            type: DataTypes.STRING(225),
            allowNull: true,
        },
        mobile: {
            type: DataTypes.STRING(15),
            allowNull: true,
        },
        emirate: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        area: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        address: {
            type: DataTypes.TEXT,
            allowNull: true,
        },
        address2: {
            type: DataTypes.TEXT,
            allowNull: true,
        },
        building_name: {
            type: DataTypes.STRING(225),
            allowNull: true,
        },
        city: {
            type: DataTypes.STRING(100),
            allowNull: true,
        },
        date: {
            type: DataTypes.DATE,
            allowNull: true,
        },
        default_address: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        latitude: {
            type: DataTypes.STRING(225),
            allowNull: true,
        },
        longitude: {
            type: DataTypes.STRING(225),
            allowNull: true,
        },
        status: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        deleted: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        guest_checkout: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        select_address: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
    },
    {
        tableName: "crm_customer_address", // important!
        timestamps: false,
    }
);

export default CrmCustomerAddress;
