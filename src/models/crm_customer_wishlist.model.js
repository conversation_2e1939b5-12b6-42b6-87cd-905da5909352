import { DataTypes } from "sequelize";
import sequelize from "../db/sequelize.js";

const CrmCustomerWishlist = sequelize.define(
    "CrmCustomerWishlist",
    {
        wishlist_id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
        },
        customer_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        country_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
            defaultValue: 1,
        },
        product_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        sku: {
            type: DataTypes.STRING(20),
            allowNull: false,
        },
        rstatus: {
            type: DataTypes.TINYINT,
            allowNull: false,
            defaultValue: 1,
        },
        created_date: {
            type: DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize.literal("CURRENT_TIMESTAMP"),
        },
        vendor_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
            defaultValue: 0,
        },
    },
    {
        tableName: "crm_customer_wishlist",
        timestamps: false,
        indexes: [
            {
                unique: true,
                fields: ["customer_id", "product_id"],
            },
        ],
    }
);

//  Define associations
CrmCustomerWishlist.associate = (models) => {
    CrmCustomerWishlist.belongsTo(models.ourshopee_users, {
        foreignKey: "customer_id",
        targetKey: "id", //PK in your users table
        as: "user",
    });

    CrmCustomerWishlist.belongsTo(models.ourshopee_products, {
        foreignKey: "product_id",
        targetKey: "id", //PK in your products table
        as: "product",
    });
};

export default CrmCustomerWishlist;
