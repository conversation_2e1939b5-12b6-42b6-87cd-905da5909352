import { DataTypes } from "sequelize";
import sequelize from "../db/sequelize.js";

const CrmRatings = sequelize.define(
    "CrmRatings",
    {
        ratings_id: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            primaryKey: true,
        },
        counrty_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
            defaultValue: 0,
        },
        vendor_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        orderno: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        productid: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        customerid: {
            type: DataTypes.INTEGER,
            allowNull: true,
            references: {
                model: "CrmCustomer",
                key: "id",
            },
        },
        rating: {
            type: DataTypes.FLOAT,
            allowNull: true,
        },
        likes: {
            type: DataTypes.JSON,
            allowNull: false,
            defaultValue: [],
        },
        dislikes: {
            type: DataTypes.JSON,
            allowNull: false,
            defaultValue: [],
        },
        review_title: {
            type: DataTypes.STRING(200),
            allowNull: true,
        },
        review: {
            type: DataTypes.TEXT("long"),
            allowNull: true,
        },
        image: {
            type: DataTypes.JSON,
            allowNull: true,
        },
        rating_date: {
            type: DataTypes.DATE,
            allowNull: true,
        },
        updated_on: {
            type: DataTypes.DATE,
            allowNull: true,
        },
        updated_by: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        rstatus: {
            type: DataTypes.TINYINT,
            allowNull: false,
            defaultValue: 1,
        },
        approved_by: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        reason: {
            type: DataTypes.STRING(255),
            allowNull: true,
        },
        isEdited: {
            type: DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: false,
        },
    },
    {
        tableName: "crm_ratings",
        timestamps: false,
    }
);

CrmRatings.associate = (models) => {
    CrmRatings.belongsTo(models.CrmCustomer, {
        foreignKey: "customerid",
        targetKey: "id",
        as: "user",
    });

    CrmRatings.belongsTo(models.CatalogProduct, {
        foreignKey: "productid",
        targetKey: "productid",
        as: "product",
    });
};

export default CrmRatings;
