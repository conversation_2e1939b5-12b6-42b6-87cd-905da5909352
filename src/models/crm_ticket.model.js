import { DataTypes } from "sequelize";
import sequelize from "../db/sequelize.js";

export const CustComplaint = sequelize.define(
    "CustComplaint",
    {
        ticket_id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
        },
        country_id: { type: DataTypes.INTEGER, allowNull: true },
        ticket_source: { type: DataTypes.STRING(15), allowNull: true },
        ticket_categoryid: { type: DataTypes.INTEGER, allowNull: true },
        ticket_type: { type: DataTypes.INTEGER, allowNull: true },
        title: { type: DataTypes.STRING(225), allowNull: true },
        description: { type: DataTypes.TEXT("long"), allowNull: true },
        proirity: { type: DataTypes.TINYINT, defaultValue: 1 }, // Note: "priority" is misspelled as "proirity" in your schema
        orderno: { type: DataTypes.STRING(100), allowNull: true },
        complaint_id: { type: DataTypes.STRING(100), allowNull: true },
        customerid: { type: DataTypes.INTEGER, allowNull: true },
        customer_name: { type: DataTypes.STRING(100), allowNull: true },
        assign_to: { type: DataTypes.INTEGER, allowNull: true },
        contact: { type: DataTypes.STRING(45), allowNull: true },
        assign_date: { type: DataTypes.DATE, allowNull: true },
        created_date: { type: DataTypes.DATE, allowNull: true },
        created_by: { type: DataTypes.INTEGER, allowNull: true },
        sla: { type: DataTypes.TINYINT, defaultValue: 0 },
        due_date: { type: DataTypes.DATE, allowNull: true },
        resolved_date: { type: DataTypes.DATE, allowNull: true },
        updateddate: { type: DataTypes.DATE, allowNull: true },
        updated_by: { type: DataTypes.INTEGER, allowNull: true },
        reopen: { type: DataTypes.INTEGER, defaultValue: 0 },
        rstatus: { type: DataTypes.TINYINT, defaultValue: 1 },
        group_action: { type: DataTypes.STRING(45), allowNull: true },
        assign_togroup: { type: DataTypes.STRING(45), allowNull: true },
    },
    {
        tableName: "crm_tickets",
        timestamps: false,
    }
);

export const CustComplaintStatus = sequelize.define(
    "CustComplaintStatus",
    {
        id: { type: DataTypes.INTEGER, primaryKey: true, autoIncrement: true },
        staff_id: { type: DataTypes.STRING(100), allowNull: false },
        assign_to: { type: DataTypes.STRING(100), allowNull: true },
        ticket_id: { type: DataTypes.STRING(100), allowNull: false },
        section: { type: DataTypes.STRING(150), allowNull: false },
        comment: { type: DataTypes.TEXT, allowNull: false },
        comment_date: { type: DataTypes.DATE, allowNull: false },
        rstatus: { type: DataTypes.TINYINT, allowNull: false },
    },
    {
        tableName: "crm_ticket_comments",
        timestamps: false,
    }
);
