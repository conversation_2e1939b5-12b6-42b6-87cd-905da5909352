// models/index.js
import CatalogCategory from "./catalog_category.model.js";
import CatalogBrand from "./catalog_brand.model.js";
import OurshopeeBrandcount from "./ourshopee_brandcount.model.js";
import CmsSection from "./cms_section.model.js";
import CmsSectionPage from "./cms_section_page.model.js";
import CmsSectionPageDetails from "./cms_section_page_details.model.js";
import CatalogProduct from "./catalog_product.model.js";
import CatalogProductInventory from "./catalog_product_inventory.model.js";
import CmsBanners from "./cms_banners.model.js";
import CrmRatings from "./crm_ratings.model.js";
import CrmCustomer from "./crm_customer.model.js";

import OmsOrders from "./oms_orders.model.js";
import OmsOrderDetail from "./oms_order_detail.model.js";
import OmsOrderHistory from "./oms_order_history.model.js";

// Collect all models
const db = {
    CatalogCategory,
    CatalogBrand,
    OurshopeeBrandcount,
    CmsSection,
    CmsSectionPage,
    CmsSectionPageDetails,
    CatalogProduct,
    CatalogProductInventory,
    CmsBanners,
    CrmRatings,
    CrmCustomer,
    OmsOrders,
    OmsOrderDetail,
    OmsOrderHistory,

};

// Run associations
Object.values(db).forEach((model) => {
    if (model.associate) {
        model.associate(db);
    }
});

export default db;
