import { DataTypes } from "sequelize";
import sequelize from "../db/sequelize.js";

const OmsCartTracking = sequelize.define(
    "OmsCartTracking",
    {
        track_id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
        },
        customer_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        ip_address: {
            type: DataTypes.STRING(20),
            allowNull: false,
        },
        session_id: {
            type: DataTypes.STRING(50),
            allowNull: true,
        },
        product_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        total_order: {
            type: DataTypes.INTEGER,
            defaultValue: 1,
        },
        action: {
            type: DataTypes.STRING(20),
            defaultValue: "add",
        },
        add_date: {
            type: DataTypes.STRING(20),
        },
        delete_date: {
            type: DataTypes.STRING(20),
        },
        checkout_date: {
            type: DataTypes.STRING(20),
        },
        action_datetime: {
            type: DataTypes.DATE,
            defaultValue: DataTypes.NOW,
        },
        rstatus: {
            type: DataTypes.INTEGER,
            defaultValue: 1,
        },
        country_id: {
            type: DataTypes.INTEGER,
        },
    },
    {
        tableName: "oms_cart_tracking",
        timestamps: false,
    }
);

export default OmsCartTracking;
