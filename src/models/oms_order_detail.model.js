import { DataTypes } from "sequelize";
import sequelize from "../db/sequelize.js";

const OmsOrderDetail = sequelize.define(
    "OmsOrderDetail",
    {
        orderdetail_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
            autoIncrement: true,
            primaryKey: true,
        },
        country_id: { type: DataTypes.INTEGER, allowNull: true },
        vendor_id: { type: DataTypes.INTEGER, allowNull: true },
        parent_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
            defaultValue: 0,
        },
        orderid: {
            type: DataTypes.INTEGER,
            allowNull: false,
            primaryKey: true,
        },
        barcode: { type: DataTypes.STRING(45), allowNull: true },
        inventory_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
            defaultValue: 0,
        },
        productid: { type: DataTypes.INTEGER, allowNull: true },
        product_sku: { type: DataTypes.STRING(12), allowNull: true },
        cost: { type: DataTypes.FLOAT, allowNull: true },
        mrp: { type: DataTypes.FLOAT, allowNull: true },
        selling_price: { type: DataTypes.FLOAT, allowNull: true },
        quantity: { type: DataTypes.INTEGER, allowNull: true },
        return_quantity: {
            type: DataTypes.INTEGER,
            allowNull: false,
            defaultValue: 0,
        },
        amount: { type: DataTypes.FLOAT, allowNull: true },
        product_status: {
            type: DataTypes.INTEGER,
            allowNull: false,
            defaultValue: 1,
        },
        rstatus: { type: DataTypes.TINYINT, allowNull: false, defaultValue: 1 },
        product_details: { type: DataTypes.STRING(500), allowNull: true },
        order_date: {
            type: DataTypes.DATE,
            allowNull: true,
            defaultValue: DataTypes.NOW,
        },
        supplierid: { type: DataTypes.INTEGER, allowNull: true },
        buyerid: { type: DataTypes.INTEGER, allowNull: true },
        migrate_date: { type: DataTypes.DATE, allowNull: true },
        os_prod_order_id: { type: DataTypes.INTEGER, allowNull: true },
        os_productid: { type: DataTypes.INTEGER, allowNull: true },
    },
    {
        tableName: "oms_order_detail",
        timestamps: false,
        indexes: [
            { fields: ["orderid"], name: "idx_orderid" },
            { fields: ["productid"], name: "idx_productid" },
            { fields: ["country_id"], name: "idx_country" },
            { fields: ["order_date"], name: "idx_order_date" },
        ],
    }
);

export default OmsOrderDetail;
