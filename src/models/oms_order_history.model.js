import { DataTypes } from "sequelize";
import sequelize from "../db/sequelize.js";

const OmsOrderHistory = sequelize.define(
    "OmsOrderHistory",
    {
        id: {
            type: DataTypes.INTEGER,
            allowNull: false,
            autoIncrement: true,
            primaryKey: true, // Composite key in SQL, but Sequelize supports one PK; see note below
        },
        orderid: {
            type: DataTypes.INTEGER,
            allowNull: false,
            primaryKey: true,
        },
        staff_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        staff_comments: {
            type: DataTypes.STRING(500),
            allowNull: false,
        },
        statusdate: {
            type: DataTypes.DATE,
            allowNull: false,
        },
        order_status_type: {
            type: DataTypes.STRING(45),
            allowNull: true,
            comment: "order_status, order_return_status, shipping_status",
        },
        order_statusid: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        status: {
            type: DataTypes.STRING(200),
            allowNull: false,
        },
        product_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        assign_to: {
            type: DataTypes.STRING(155),
            allowNull: true,
        },
    },
    {
        tableName: "oms_order_history",
        timestamps: false,
        indexes: [
            { fields: ["order_statusid"], name: "idx_statusid" },
            { fields: ["product_id"], name: "idx_product_id" },
        ],
    }
);

export default OmsOrderHistory;
