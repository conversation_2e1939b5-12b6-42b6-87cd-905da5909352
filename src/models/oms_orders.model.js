import { DataTypes } from "sequelize";
import sequelize from "../db/sequelize.js";

const OmsOrders = sequelize.define(
    "OmsOrders",
    {
        orderid: {
            type: DataTypes.INTEGER,
            allowNull: false,
            autoIncrement: true,
            primaryKey: true,
        },
        type: {
            type: DataTypes.STRING(45),
            defaultValue: "order",
            comment: "order or return",
        },
        vendor_id: { type: DataTypes.INTEGER, allowNull: true },
        country_id: { type: DataTypes.INTEGER, allowNull: true },
        order_refid: { type: DataTypes.INTEGER, allowNull: true },
        order_ref_code: { type: DataTypes.STRING(20), allowNull: true },
        parent_orderid: { type: DataTypes.INTEGER, allowNull: true },
        customer_contact: { type: DataTypes.INTEGER, allowNull: true },
        order_date: {
            type: DataTypes.DATE,
            defaultValue: DataTypes.NOW,
        },
        itemcount: {
            type: DataTypes.INTEGER,
            defaultValue: 0,
        },
        discount_amount: {
            type: DataTypes.FLOAT,
            defaultValue: 0,
        },
        credit_amount: {
            type: DataTypes.FLOAT,
            defaultValue: 0,
        },
        promotionid: {
            type: DataTypes.INTEGER,
            defaultValue: 0,
        },
        sub_total: { type: DataTypes.FLOAT, allowNull: true },
        tax_amount: { type: DataTypes.FLOAT, allowNull: true },
        shipping_charges: {
            type: DataTypes.FLOAT,
            defaultValue: 0,
        },
        processing_fee: {
            type: DataTypes.FLOAT,
            defaultValue: 0,
        },
        promo_code: {
            type: DataTypes.STRING(20),
            allowNull: true,
        },
        total_amount: {
            type: DataTypes.DECIMAL(10, 2),
            allowNull: true,
        },
        payment_method: { type: DataTypes.STRING(20), allowNull: true },
        payment_methodid: {
            type: DataTypes.INTEGER,
            defaultValue: 4,
            comment: "this data get from master table",
        },
        payment_status: {
            type: DataTypes.STRING(5),
            defaultValue: "Due",
            comment: "this will be Due or Paid",
        },
        gway_paymentmethod: { type: DataTypes.TEXT, allowNull: true },
        gway_order_id: { type: DataTypes.STRING(50), allowNull: true },
        gway_transaction_id: { type: DataTypes.STRING(50), allowNull: true },
        gway_status: { type: DataTypes.STRING(45), allowNull: true },
        order_duedate: { type: DataTypes.DATEONLY, allowNull: true },
        updated_date: {
            type: DataTypes.DATE,
            defaultValue: DataTypes.NOW,
        },
        staff_id: { type: DataTypes.INTEGER, allowNull: true },
        coupon_amount: { type: DataTypes.FLOAT, allowNull: true },
        donation_fee: {
            type: DataTypes.FLOAT,
            defaultValue: 0,
        },
        billingaddress: { type: DataTypes.TEXT, allowNull: true },
        shippingaddress: { type: DataTypes.TEXT, allowNull: true },
        channel: { type: DataTypes.STRING(45), allowNull: true },
        notes: { type: DataTypes.TEXT("long"), allowNull: true },
        order_statusid: {
            type: DataTypes.INTEGER,
            defaultValue: 1,
        },
        order_status: {
            type: DataTypes.STRING(20),
            defaultValue: "0",
        },
        customerid: { type: DataTypes.INTEGER, allowNull: true },
        customer_details: { type: DataTypes.TEXT, allowNull: true },
        same_day_delivery: { type: DataTypes.INTEGER, allowNull: true },
        available_time: { type: DataTypes.INTEGER, allowNull: true },
        os_order_id: {
            type: DataTypes.INTEGER,
            defaultValue: 0,
        },
        orderfrom: { type: DataTypes.STRING(45), allowNull: true },
        order_from_id: { type: DataTypes.INTEGER, allowNull: true },
        invoice_no: { type: DataTypes.STRING(30), allowNull: true },
        invoice_date: { type: DataTypes.DATE, allowNull: true },
        area_id: { type: DataTypes.INTEGER, allowNull: true },
        delivery_date: { type: DataTypes.DATEONLY, allowNull: true },
        oms_orderscol: { type: DataTypes.STRING(45), allowNull: true },
        driver_id: {
            type: DataTypes.INTEGER,
            defaultValue: 0,
        },
        shipping_statusid: {
            type: DataTypes.INTEGER,
            defaultValue: 0,
            comment:
                "its like order_status ids.... we can get from master table",
        },
        driver_status: {
            type: DataTypes.INTEGER,
            defaultValue: 0,
            comment: "0 - Not accepted yet, 1- Accepted",
        },
        order_return_status: {
            type: DataTypes.INTEGER,
            defaultValue: 0,
            comment:
                "its like order_status ids.... we can get from master table",
        },
    },
    {
        tableName: "oms_orders",
        timestamps: false,
        indexes: [
            { fields: ["order_refid"], name: "idx_order_refid" },
            { fields: ["order_date"], name: "idx_order_date" },
            { fields: ["customerid"], name: "idx_customer" },
            { fields: ["vendor_id"], name: "idx_vendor" },
            { fields: ["country_id"], name: "idx_country" },
            { fields: ["order_statusid", "order_status"], name: "idx_status" },
        ],
    }
);

OmsOrders.associate = (models) => {
    OmsOrders.hasMany(models.OmsOrderDetail, {
        as: "order_details",
        foreignKey: "orderid",
        sourceKey: "orderid",
    });
    OmsOrders.hasMany(models.OmsOrderHistory, {
        as: "order_history",
        foreignKey: "orderid",
        sourceKey: "orderid",
    });
};

export default OmsOrders;
