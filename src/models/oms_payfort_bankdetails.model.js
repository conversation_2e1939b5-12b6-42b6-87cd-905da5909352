import { DataTypes } from "sequelize";
import sequelize from "../db/sequelize.js";

const OmsPayfortBankdetails = sequelize.define(
    "OmsPayfortBankdetails",
    {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
        },
        bank_name: { type: DataTypes.STRING },
        logo: { type: DataTypes.STRING },
        logo_png: { type: DataTypes.STRING },
        "3-month-intrest": { type: DataTypes.STRING },
        "3-month-processing": { type: DataTypes.STRING },
        "6-month-intrest": { type: DataTypes.STRING },
        "6-month-processing": { type: DataTypes.STRING },
        "9-month-intrest": { type: DataTypes.STRING },
        "9-month-processing": { type: DataTypes.STRING },
        "12-month-intrest": { type: DataTypes.STRING },
        "12-month-processing": { type: DataTypes.STRING },
        "18-month-intrest": { type: DataTypes.STRING },
        "18-month-processing": { type: DataTypes.STRING },
        "24-month-intrest": { type: DataTypes.STRING },
        "24-month-processing": { type: DataTypes.STRING },
        "36-month-intrest": { type: DataTypes.STRING },
        "36-month-processing": { type: DataTypes.STRING },
        processing_fee_type: { type: DataTypes.STRING },
        status: { type: DataTypes.INTEGER },
    },
    {
        tableName: "oms_payfort_bankdetails",
        timestamps: false,
    }
);

export default OmsPayfortBankdetails;
