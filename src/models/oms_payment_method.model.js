import { DataTypes } from "sequelize";
import sequelize from "../db/sequelize.js";

const OmsPaymentMethod = sequelize.define(
    "OmsPaymentMethod",
    {
        id: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            primaryKey: true,
        },
        country_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        payment_type: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        name: {
            type: DataTypes.STRING(200),
            allowNull: false,
        },
        value: {
            type: DataTypes.STRING(225),
            allowNull: false,
        },
        title: {
            type: DataTypes.STRING(225),
            allowNull: false,
        },
        processing_fee: {
            type: DataTypes.DECIMAL(10, 2),
            allowNull: false,
        },
        icon: {
            type: DataTypes.STRING(225),
            allowNull: false,
        },
        position: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        gateway_type: {
            type: DataTypes.INTEGER,
            allowNull: false,
            defaultValue: 0,
        },
        default_sel: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        status: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        pay_percentage: {
            type: DataTypes.DECIMAL(10, 3),
            allowNull: false,
        },
    },
    {
        tableName: "oms_payment_method",
        timestamps: false, // No createdAt/updatedAt fields
    }
);

export default OmsPaymentMethod;
