import { DataTypes } from "sequelize";
import sequelize from "../db/sequelize.js";

const OmsWebfeed = sequelize.define(
    "OmsWebfeed",
    {
        webfeed_id: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            primaryKey: true,
        },
        country_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        customer: {
            type: DataTypes.STRING(200),
            allowNull: false,
        },
        code: {
            type: DataTypes.STRING(5),
            allowNull: true,
        },
        mobileno: {
            type: DataTypes.STRING(15),
            allowNull: false,
        },
        product_code: {
            type: DataTypes.STRING(100),
            allowNull: false,
        },
        productid: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        order_from: {
            type: DataTypes.STRING(225),
            allowNull: false,
            defaultValue: "WebFeed",
        },
        emirate: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        area: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        address: {
            type: DataTypes.STRING(225),
            allowNull: false,
        },
        order_date: {
            type: DataTypes.DATE,
            allowNull: false,
        },
        price: {
            type: DataTypes.DECIMAL(10, 2),
            allowNull: false,
        },
        quantity: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        cancel_date: {
            type: DataTypes.DATE,
            defaultValue: "0000-00-00 00:00:00",
        },
        cancel_by: {
            type: DataTypes.INTEGER,
            defaultValue: 0,
        },
        status: {
            type: DataTypes.STRING(100),
            allowNull: false,
        },
    },
    {
        tableName: "oms_webfeed",
        timestamps: false,
    }
);

export default OmsWebfeed;
