import { DataTypes } from "sequelize";
import sequelize from "../db/sequelize.js";

const TmpOtpVerification = sequelize.define(
    "TmpOtpVerification",
    {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            allowNull: false,
        },
        email_number: {
            type: DataTypes.STRING(100),
            allowNull: false,
        },
        otp: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        created_date: {
            type: DataTypes.DATE,
            allowNull: true,
        },

        sendtime: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
    },
    {
        tableName: "otp_verification",
        timestamps: false,
    }
);

export default TmpOtpVerification;
