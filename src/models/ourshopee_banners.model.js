import { DataTypes } from "sequelize";
import sequelize from "../db/sequelize.js";

const OurshopeeBanner = sequelize.define(
  "OurshopeeBanner",
  {
    id: { type: DataTypes.INTEGER, autoIncrement: true, primaryKey: true },
    country_id: { type: DataTypes.INTEGER, allowNull: false },
    url: { type: DataTypes.STRING(275), allowNull: false },
    mobile_url: { type: DataTypes.TEXT, allowNull: false },
    image: { type: DataTypes.STRING(225), allowNull: false },
    position: { type: DataTypes.INTEGER, allowNull: false },
    arabic_url: { type: DataTypes.STRING(225), allowNull: false },
    arabicmobile_url: { type: DataTypes.STRING(225), allowNull: false },
    arabic_image: { type: DataTypes.STRING(225), allowNull: false },
    status: { type: DataTypes.TINYINT, allowNull: false },
  },
  {
    tableName: "ourshopee_banners",
    timestamps: false,
    indexes: [{ fields: ["country_id", "status", "position"] }],
  }
);

export default OurshopeeBanner;
