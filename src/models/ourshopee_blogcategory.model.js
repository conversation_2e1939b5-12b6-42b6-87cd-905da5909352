import { DataTypes } from "sequelize";
import sequelize from "../db/sequelize.js";

const OurshopeeBlogCategory = sequelize.define(
    "OurshopeeBlogCategory",
    {
        id: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            primaryKey: true,
        },
        name: {
            type: DataTypes.STRING(225),
            allowNull: false,
            unique: true,
        },
        url: {
            type: DataTypes.TEXT,
            allowNull: false,
        },
        position: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        post_date: {
            type: DataTypes.DATE,
            allowNull: false,
        },
        staff_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
    },
    {
        tableName: "ourshopee_blogcategory",
        timestamps: false,
    }
);

export default OurshopeeBlogCategory;
