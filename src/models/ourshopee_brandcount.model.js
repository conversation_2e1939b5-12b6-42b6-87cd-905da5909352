import { DataTypes } from "sequelize";
import sequelize from "../db/sequelize.js";
import OurshopeeBrand from "./catalog_brand.model.js";

const OurshopeeBrandcount = sequelize.define(
    "OurshopeeBrandcount",
    {
        id: { type: DataTypes.INTEGER, autoIncrement: true, primaryKey: true },
        category_id: { type: DataTypes.INTEGER, allowNull: false },
        subcategory_id: { type: DataTypes.INTEGER, allowNull: false },
        sub_subcategory_id: { type: DataTypes.BIGINT, allowNull: false },
        brand_id: { type: DataTypes.INTEGER, allowNull: false },
        count: { type: DataTypes.BIGINT, allowNull: false },
        front_view: { type: DataTypes.INTEGER, allowNull: false },
    },
    {
        tableName: "ourshopee_brandcount",
        timestamps: false,
        indexes: [
            {
                name: "PRIMARY",
                unique: true,
                fields: ["id"],
            },
            {
                name: "subcategory_id",
                fields: ["subcategory_id"],
            },
        ],
    }
);

OurshopeeBrandcount.associate = () => {
    OurshopeeBrandcount.belongsTo(OurshopeeBrand, {
        foreignKey: "brand_id",
        targetKey: "brandid",
        as: "brand",
    });
};

export default OurshopeeBrandcount;
