import { DataTypes } from "sequelize";
import sequelize from "../db/sequelize.js";

const OurshopeeCartTracking = sequelize.define(
  "OurshopeeCartTracking",
  {
    track_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    ip_address: {
      type: DataTypes.STRING(20),
      allowNull: false,
    },
    session_id: {
      type: DataTypes.STRING(50),
      allowNull: false,
    },
    product_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    total_order: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1,
    },
    action: {
      type: DataTypes.STRING(20),
      allowNull: false,
      defaultValue: "add",
    },
    add_date: {
      type: DataTypes.STRING(20),
      allowNull: false,
    },
    delete_date: {
      type: DataTypes.STRING(20),
      allowNull: false,
    },
    checkout_date: {
      type: DataTypes.STRING(20),
      allowNull: false,
    },
    action_datetime: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    rstatus: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 1,
    },
    country_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
  },
  {
    tableName: "ourshopee_cart_tracking",
    timestamps: false,
  }
);

export default OurshopeeCartTracking;
