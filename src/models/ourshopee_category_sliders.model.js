import { DataTypes } from "sequelize";
import sequelize from "../db/sequelize.js";

const CategorySlider = sequelize.define(
  "CategorySlider",
  {
    id: { type: DataTypes.INTEGER, autoIncrement: true, primaryKey: true },
    country_id: { type: DataTypes.INTEGER, allowNull: false },
    category_id: { type: DataTypes.INTEGER, allowNull: false },
    en_url: { type: DataTypes.TEXT, allowNull: false },
    ar_url: { type: DataTypes.TEXT, allowNull: false },
    en_image: { type: DataTypes.STRING(275), allowNull: false },
    ar_image: { type: DataTypes.STRING(275), allowNull: false },
    en_mobile_image: { type: DataTypes.STRING(275), allowNull: false },
    ar_mobile_image: { type: DataTypes.STRING(275), allowNull: false },
    subcategory_id: { type: DataTypes.INTEGER, allowNull: false },
    tag_id: { type: DataTypes.STRING(50), allowNull: false },
    brand_id: { type: DataTypes.INTEGER, allowNull: false },
    product_id: { type: DataTypes.BIGINT, allowNull: false },
    position: { type: DataTypes.INTEGER, allowNull: false },
    status: { type: DataTypes.TINYINT, allowNull: false, defaultValue: 1 },
  },
  {
    tableName: "ourshopee_category_sliders",
    timestamps: false,
  }
);

export default CategorySlider;
