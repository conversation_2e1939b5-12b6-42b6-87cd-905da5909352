// src/models/ourshopee_country.model.js
import { DataTypes } from "sequelize";
import sequelize from "../db/sequelize.js";

const OurshopeeCountry = sequelize.define(
  "OurshopeeCountry",
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      unique: true,
    },
    name_arabic: {
      type: DataTypes.STRING(225),
      allowNull: false,
    },
    prefix: {
      type: DataTypes.STRING(200),
      allowNull: false,
    },
    folder_name: {
      type: DataTypes.STRING(25),
      allowNull: false,
    },
    sm_link: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    currency: {
      type: DataTypes.STRING(20),
      allowNull: false,
    },
    flag_image: {
      type: DataTypes.STRING(191),
      allowNull: false,
    },
    website_link: {
      type: DataTypes.STRING(225),
      allowNull: false,
    },
    web_flag_image: {
      type: DataTypes.STRING(225),
      allowNull: false,
    },
    web_flag_png: {
      type: DataTypes.STRING(225),
      allowNull: false,
    },
    country_phone_code: {
      type: DataTypes.STRING(191),
      allowNull: false,
    },
    arabic_currency: {
      type: DataTypes.STRING(191),
      allowNull: false,
    },
    currency_value: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
    },
    store_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    country_supplier_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
    },
    ip: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    mobile: {
      type: DataTypes.STRING(25),
      allowNull: false,
    },
    whats_app: {
      type: DataTypes.STRING(25),
      allowNull: false,
    },
    transfer_charge: {
      type: DataTypes.DECIMAL(10, 3),
      allowNull: false,
    },
    status: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    vat: {
      type: DataTypes.DECIMAL(10, 3),
      allowNull: false,
    },
  },
  {
    tableName: "ourshopee_country",
    timestamps: false,
    indexes: [{ unique: true, fields: ["name"] }],
  }
);

export default OurshopeeCountry;
