import { DataTypes } from "sequelize";
import sequelize from "../db/sequelize.js";
import CrmCustomer from "./crm_customer.model.js";

const OurshopeeErrorLog = sequelize.define(
    "OurshopeeErrorLog",
    {
        id: { type: DataTypes.INTEGER, autoIncrement: true, primaryKey: true },

        user_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
            references: {
                model: CrmCustomer,
                key: "id",
            },
        },

        route: { type: DataTypes.STRING, allowNull: false },
        message: { type: DataTypes.TEXT, allowNull: false },
        stack: { type: DataTypes.TEXT },
        method: { type: DataTypes.STRING },
        payload: { type: DataTypes.JSON },
        created_at: { type: DataTypes.DATE, defaultValue: DataTypes.NOW },
    },
    {
        tableName: "error_logs",
        timestamps: false,
    }
);

export default OurshopeeErrorLog;
