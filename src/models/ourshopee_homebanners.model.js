import { DataTypes } from "sequelize";
import sequelize from "../db/sequelize.js";
import OurshopeeHomeCategory from "./ourshopee_homecategory.model.js";

const OurshopeeHomeBanner = sequelize.define(
  "OurshopeeHomeBanner",
  {
    id: { type: DataTypes.INTEGER, autoIncrement: true, primaryKey: true },
    country_id: { type: DataTypes.INTEGER, allowNull: false },
    category_id: { type: DataTypes.INTEGER, allowNull: false },
    url: { type: DataTypes.INTEGER, allowNull: false },
    mobile_url: { type: DataTypes.TEXT, allowNull: false },
    image: { type: DataTypes.STRING(225), allowNull: false },
    position: { type: DataTypes.INTEGER, allowNull: false },
    arabic_url: { type: DataTypes.STRING(225), allowNull: false },
    arabic_mobile_url: { type: DataTypes.STRING(225), allowNull: false },
    arabic_image: { type: DataTypes.STRING(225), allowNull: false },
    status: { type: DataTypes.TINYINT, allowNull: false },
    created_date: { type: DataTypes.DATE, allowNull: true },
    created_by: { type: DataTypes.INTEGER, allowNull: true },
    updated_date: { type: DataTypes.DATE, allowNull: true },
    updated_by: { type: DataTypes.INTEGER, allowNull: true },
  },
  {
    tableName: "ourshopee_homebanners",
    timestamps: false,
    indexes: [{ fields: ["country_id", "status", "position"] }],
  }
);

// relationship with homecategory
OurshopeeHomeBanner.belongsTo(OurshopeeHomeCategory, {
  foreignKey: "category_id",
  targetKey: "id",
  as: "category",
});

export default OurshopeeHomeBanner;
