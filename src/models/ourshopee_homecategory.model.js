import { DataTypes } from "sequelize";
import sequelize from "../db/sequelize.js";

const OurshopeeHomeCategory = sequelize.define(
  "OurshopeeHomeCategory",
  {
    id: { type: DataTypes.INTEGER, autoIncrement: true, primaryKey: true },
    country_id: { type: DataTypes.INTEGER, allowNull: false },
    name: { type: DataTypes.STRING(200), allowNull: true },
    name_arabic: { type: DataTypes.STRING(225), allowNull: false },
    status: { type: DataTypes.TINYINT, allowNull: true },
    position: { type: DataTypes.INTEGER, allowNull: true },
    created_date: { type: DataTypes.DATE, allowNull: true },
    created_by: { type: DataTypes.INTEGER, allowNull: true },
    updated_date: { type: DataTypes.DATE, allowNull: true },
    updated_by: { type: DataTypes.INTEGER, allowNull: true },
  },
  {
    tableName: "ourshopee_homecategory",
    timestamps: false,
    indexes: [{ fields: ["country_id", "status", "position"] }],
  }
);

export default OurshopeeHomeCategory;
