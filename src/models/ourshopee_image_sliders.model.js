import { DataTypes } from "sequelize";
import sequelize from "../db/sequelize.js";

const OurshopeeImageSlider = sequelize.define(
  "OurshopeeImageSlider",
  {
    id: { type: DataTypes.INTEGER, autoIncrement: true, primaryKey: true },
    country_id: { type: DataTypes.INTEGER, allowNull: false },
    url: { type: DataTypes.TEXT, allowNull: false },
    mobile_url: { type: DataTypes.TEXT, allowNull: false },
    image: { type: DataTypes.STRING(275), allowNull: false },
    mobile_image: { type: DataTypes.STRING(275), allowNull: false },
    banner_type: { type: DataTypes.STRING(50), allowNull: false },
    parent_id: { type: DataTypes.INTEGER, allowNull: false },
    child_id: { type: DataTypes.INTEGER, allowNull: false },
    position: { type: DataTypes.INTEGER, allowNull: false },
    status: { type: DataTypes.TINYINT, allowNull: false },
    mobile_status: { type: DataTypes.INTEGER, allowNull: false },
    arabic_url: { type: DataTypes.STRING(225), allowNull: false },
    arabicmobile_url: { type: DataTypes.STRING(225), allowNull: false },
    arabic_image: { type: DataTypes.STRING(225), allowNull: false },
    arabic_mobile_image: { type: DataTypes.STRING(225), allowNull: false },
  },
  {
    tableName: "ourshopee_image_sliders",
    timestamps: false,
    indexes: [{ fields: ["country_id", "status", "position"] }],
  }
);

export default OurshopeeImageSlider;
