import { DataTypes } from "sequelize";
import sequelize from "../db/sequelize.js";

const OurshopeeNewsletter = sequelize.define(
  "OurshopeeNewsletter",
  {
    id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
    },
    email: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    status: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    email_status: {
      type: DataTypes.TINYINT,
      allowNull: false,
    },
  },
  {
    tableName: "ourshopee_newsletter",
    timestamps: false,
  }
);

export default OurshopeeNewsletter;
