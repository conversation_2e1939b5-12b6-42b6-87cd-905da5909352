import { DataTypes } from "sequelize";
import sequelize from "../db/sequelize.js";

const OurshopeeSectionImages = sequelize.define(
    "OurshopeeSectionImages",
    {
        id: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            primaryKey: true,
        },
        country_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        url: {
            type: DataTypes.TEXT,
            allowNull: false,
        },
        mobile_url: {
            type: DataTypes.TEXT,
            allowNull: false,
        },
        image: {
            type: DataTypes.STRING(275),
            allowNull: false,
        },
        section_id: {
            type: DataTypes.BIGINT,
            allowNull: false,
        },
        position: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        status: {
            type: DataTypes.TINYINT,
            allowNull: false,
        },
        arabic_url: {
            type: DataTypes.STRING(225),
            allowNull: false,
        },
        arabicmobile_url: {
            type: DataTypes.STRING(225),
            allowNull: false,
        },
        arabic_image: {
            type: DataTypes.STRING(225),
            allowNull: false,
        },
        mobile_image: {
            type: DataTypes.STRING(225),
            allowNull: false,
        },
        arabic_mobile_image: {
            type: DataTypes.STRING(225),
            allowNull: false,
        },
    },
    {
        tableName: "ourshopee_section_images",
        timestamps: false,
    }
);

export default OurshopeeSectionImages;
