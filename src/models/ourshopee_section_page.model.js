import { DataTypes } from "sequelize";
import sequelize from "../db/sequelize.js";
import CmsSectionPageDetails from "./cms_section_page_details.model.js";

const CmsSectionPage = sequelize.define(
    "CmsSectionPage",
    {
        id: {
            type: DataTypes.INTEGER.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
        },
        section_id: {
            type: DataTypes.INTEGER.UNSIGNED,
            allowNull: false,
        },
        title_en: {
            type: DataTypes.TEXT,
            allowNull: false,
        },
        title_ar: {
            type: DataTypes.TEXT,
            allowNull: false,
        },
        html_type: {
            type: DataTypes.INTEGER.UNSIGNED,
            allowNull: false,
        },
        outer_css: {
            type: DataTypes.TEXT,
            allowNull: false,
        },
        main_css: {
            type: DataTypes.STRING(225),
            allowNull: false,
        },
        list_css: {
            type: DataTypes.STRING(225),
            allowNull: false,
        },
        title_display: {
            type: DataTypes.STRING(25),
            allowNull: false,
        },
        bg_format: {
            type: DataTypes.STRING(225),
            allowNull: false,
        },
        text_format: {
            type: DataTypes.STRING(225),
            allowNull: false,
        },
        position: {
            type: DataTypes.INTEGER.UNSIGNED,
            allowNull: false,
        },
        status: {
            type: DataTypes.BOOLEAN,
            defaultValue: true,
        },
        updated_by: {
            type: DataTypes.INTEGER.UNSIGNED,
            allowNull: false,
        },
        updated_date: {
            type: DataTypes.DATE,
            allowNull: false,
        },
    },
    {
        tableName: "ourshopee_section_page",
        timestamps: false,
    }
);

CmsSectionPage.associate = () => {
    CmsSectionPage.hasMany(CmsSectionPageDetails, {
        foreignKey: "page_id",
        sourceKey: "id",
        as: "details",
    });
};

export default CmsSectionPage;
