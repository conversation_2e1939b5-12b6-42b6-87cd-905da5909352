import { DataTypes } from "sequelize";
import sequelize from "../db/sequelize.js";
import OurshopeeBrand from "./catalog_brand.model.js";

const OurshopeeAllSlider = sequelize.define(
    "OurshopeeAllSlider",
    {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
        },
        slider_type: { type: DataTypes.STRING(50), allowNull: false },
        country_id: { type: DataTypes.INTEGER, allowNull: false },
        category_id: { type: DataTypes.INTEGER, allowNull: false },
        subcategory_id: { type: DataTypes.INTEGER, allowNull: false },
        tag_id: { type: DataTypes.STRING(50), allowNull: false },
        brand_id: { type: DataTypes.INTEGER, allowNull: false },
        product_id: { type: DataTypes.STRING(50), allowNull: false },
        position: { type: DataTypes.INTEGER, allowNull: false },
        en_url: { type: DataTypes.STRING(255), allowNull: false },
        ar_url: { type: DataTypes.STRING(255), allowNull: false },
        en_image: { type: DataTypes.STRING(275), allowNull: false },
        ar_image: { type: DataTypes.STRING(275), allowNull: false },
        en_mobile_image: { type: DataTypes.STRING(275), allowNull: false },
        ar_mobile_image: { type: DataTypes.STRING(275), allowNull: false },
        status: { type: DataTypes.TINYINT, allowNull: false, defaultValue: 1 },
    },
    {
        tableName: "ourshopee_all_sliders",
        timestamps: false,
        indexes: [
            { fields: ["brand_id"] },
            { fields: ["country_id"] },
            { fields: ["category_id"] },
            { fields: ["subcategory_id"] },
            { fields: ["position"] },
        ],
    }
);

// Define association
OurshopeeAllSlider.belongsTo(OurshopeeBrand, {
    foreignKey: "brand_id",
    targetKey: "brandid",
    as: "brand",
});

OurshopeeBrand.hasMany(OurshopeeAllSlider, {
    foreignKey: "brand_id",
    sourceKey: "brandid",
    as: "sliders",
});

export default OurshopeeAllSlider;
