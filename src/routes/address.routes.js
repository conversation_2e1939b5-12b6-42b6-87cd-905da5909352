import {
    getAllAddresses,
    addAndUpdateAddress,
    saveDefaultAddress,
    deleteUserAddress,
    selectAddress,
    changeAddressStatus,
} from "../controllers/address.controllers.js";
import { verifyJWT } from "../middlewares/auth.middlewares.js";

export default async function addressRoutes(fastify) {
    fastify.addHook("preHandler", verifyJWT);

    fastify.get("/getalladdresses", getAllAddresses);
    fastify.post("/postUserAddress", addAndUpdateAddress);
    fastify.post("/saveDefaultAddress", saveDefaultAddress);
    fastify.post("/deleteUserAddress", deleteUserAddress);
    fastify.post("/selectaddress", selectAddress);
    fastify.post("/updatestatus", changeAddressStatus);
}
