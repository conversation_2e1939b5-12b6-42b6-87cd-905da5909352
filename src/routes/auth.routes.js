import {
    checkEmailSchema,
    checkMobileSchema,
    verifyOTPSchema,
    userSignUpSchema,
    userSignInSchema,
    forgotPasswordSchema,
    verifyForgotPasswordTokenSchema,
    resetPasswordSchema,
} from "../validators/auth.validators.js";

import {
    isEmailExist,
    checkMobile,
    verifyOTP,
    signUp,
    signIn,
    forgotPassword,
    verifyForgotPasswordToken,
    resetPassword,
    reSendOtp,
    reSendOtpWhatsapp,
    sendEnquiryMails,
} from "../controllers/auth.controllers.js";

export default async function authRoutes(fastify) {
    fastify.post("/CheckEmail", { schema: checkEmailSchema }, isEmailExist);

    fastify.post("/checkMobile", { schema: checkMobileSchema }, checkMobile);

    fastify.post("/verifyMobileOtp", { schema: verifyOTPSchema }, verifyOTP);

    fastify.post("/Signup", { schema: userSignUpSchema }, signUp);

    fastify.post("/Login", { schema: userSignInSchema }, signIn);

    fastify.post(
        "/forgotPassword",
        { schema: forgotPasswordSchema },
        forgotPassword
    );

    fastify.get(
        "/reset-password/*",
        { schema: verifyForgotPasswordTokenSchema },
        verifyForgotPasswordToken
    );

    fastify.post(
        "/reset-password/*",
        { schema: resetPasswordSchema },
        resetPassword
    );

    fastify.post("/reSendOtp", { schema: checkMobileSchema }, reSendOtp);

    fastify.post(
        "/reSendOtpWhatsapp",
        { schema: checkMobileSchema },
        reSendOtpWhatsapp
    );
    fastify.post("/sendEnquiryMails", sendEnquiryMails);
}
