import {
    addToCart,
    getFromCart,
    ChangeCartQuantity,
    getPlaceOrder,
    RemoveFromCart,
    PostPlaceOrder,
    DirectBuy,
    getDirectPlaceOrder,
    UpdateCartStatus,
    GetUserDefaultAddress,
    validateCoupon,
} from "../controllers/cart.controllers.js";
import { verifyJWT } from "../middlewares/auth.middlewares.js";

export default async function cartRoutes(fastify) {
    fastify.post("/AddToCart", addToCart);
    fastify.post("/GetFromCart", getFromCart);
    fastify.post("/changeCartQuantity", ChangeCartQuantity);
    fastify.post("/removeFromCart", RemoveFromCart);
    fastify.post(
        "/updateCartStatus",
        { preHandler: verifyJWT },
        UpdateCartStatus
    );
    fastify.get("/GetPlaceOrder", getPlaceOrder);
    fastify.post(
        "/GetDirectPlaceOrder",
        { preHandler: verifyJWT },
        getDirectPlaceOrder
    );
    fastify.post("/getuserdefaultaddress", GetUserDefaultAddress);
    fastify.post(
        "/postpostPlaceOrder",
        { preHandler: verifyJWT },
        PostPlaceOrder
    );
    fastify.post("/directBuy", { preHandler: verifyJWT }, DirectBuy);
    fastify.post("/checkCouponCode", { preHandler: verifyJWT }, validateCoupon);
}
