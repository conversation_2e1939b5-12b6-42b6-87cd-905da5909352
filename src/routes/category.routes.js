import {
    getAllCategoryItems,
    getAllItems,
    subcategoryMetatags,
    subsubcategoryMetatags,
    brandsMetatags,
    getAllCategories,
} from "../controllers/category.controllers.js";

export default async function categoryRoutes(fastify) {
    fastify.get("/getcategorylist", getAllCategories);
    fastify.get("/getallcategoryItems", getAllCategoryItems);
    fastify.get("/getallitems", getAllItems);
    fastify.post("/subcategorymetatags", subcategoryMetatags);
    fastify.post("/subsubcategorymetatag", subsubcategoryMetatags);
    fastify.post("/brandsmetatags", brandsMetatags);
}
