import {
    getUserWishlist,
    addAndRemoveWishList,
    getMyProfile,
    updateMyProfile,
    changePassword,
} from "../controllers/profile.controllers.js";
import { verifyJWT } from "../middlewares/auth.middlewares.js";

export default async function profileRoutes(fastify) {
    fastify.addHook("preHandler", verifyJWT);

    fastify.post("/postWishList", addAndRemoveWishList);
    fastify.get("/getWishLists", getUserWishlist);
    fastify.get("/getMyProfile", getMyProfile);
    fastify.post("/updateMyProfile", updateMyProfile);
    fastify.post("/changePassword", changePassword);
}
