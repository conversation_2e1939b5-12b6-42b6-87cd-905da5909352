import {
    getBlogs,
    getBlogByCategoryId,
    clearanceSale,
    dealOfTheDay,
    topSellingProducts,
    saverZone,
    metaTags,
    getSpecialDealsProducts,
    getComboDealsProducts,
    getEidResultProducts,
    getInfinteScrollItems,
} from "../controllers/section.controllers.js";

export default async function sectionRoutes(fastify) {
    fastify.get("/blogs", getBlogs);
    fastify.get("/blogByCatId", getBlogByCategoryId);
    fastify.get("/clearance_sale", clearanceSale);
    fastify.get("/deal_of_the_day", dealOfTheDay);
    fastify.get("/top_selling_products", topSellingProducts);
    fastify.get("/saver_zone1", saverZone);
    fastify.get("/metatags", metaTags);
    fastify.get("/special_deals_products/:sectionId", getSpecialDealsProducts);
    fastify.get("/combo_deals_products/:sec_id", getComboDealsProducts);
    fastify.get("/eid_result_products", getEidResultProducts);
    fastify.get("/getInfinteScrollItems", getInfinteScrollItems);
}
