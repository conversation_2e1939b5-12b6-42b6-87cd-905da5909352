import CrmCustomerAddress from "../models/crm_customer_address.model.js";
import { currentDateTime } from "../utils/dateTimeHandler.js";

export const getAllAddressesService = async (req) => {
    try {
        const country_id = parseInt(req.country_id);
        const customer_id = req.user?.user_id;
        const idaddress = parseInt(req.query?.idaddress || 0);

        const where = {
            customer_id,
            country_id,
            ...(idaddress > 0 && { id: idaddress }),
        };

        const result = await CrmCustomerAddress.findAll({ where, raw: true });

        if (!result.length) {
            return {
                statusCode: 200,
                message: "Address not found!",
                status: "failure",
                result: [],
            };
        }

        const output = result.map((el) => ({
            idaddress: el.id,
            first_name: el.name,
            last_name: el.last_name,
            company: el.company,
            mobile: el.mobile,
            emirate: el.emirate,
            area: el.area,
            address: el.address,
            address2: el.address2,
            building_name: el.building_name,
            latitude: el.latitude,
            longitude: el.longitude,
            default_address: el.default_address,
            status: el.status,
            select_address: el.select_address,
        }));

        return {
            statusCode: 200,
            message: "Address fetched successfully!",
            status: "success",
            result: output,
        };
    } catch (err) {
        throw err;
    }
};

export const addAndUpdateAddressService = async (req) => {
    try {
        const customer_id = req.user?.user_id;
        const { country_id } = req.country_id;

        const {
            idaddress = 0,
            first_name,
            company,
            mobile,
            emirate,
            area,
            address,
            address2,
            building_name,
            latitude,
            longitude,
            default_address = 0,
            status = 0,
            select_address,
            deleted = 0,
            city = "",
        } = req.body;

        const data = {
            customer_id,
            country_id: req.country_id,
            name: first_name,
            last_name: "",
            company,
            mobile,
            emirate,
            area,
            address,
            address2,
            building_name,
            latitude,
            longitude,
            default_address,
            status,
            select_address,
            deleted,
            city,
        };

        const addresses = await CrmCustomerAddress.findAll({
            where: { customer_id },
        });

        if (addresses && default_address == 1) {
            await CrmCustomerAddress.update(
                { default_address: 0 },
                { where: { customer_id } }
            );
        }

        let addressEntry;

        if (idaddress > 0) {
            await CrmCustomerAddress.update(data, {
                where: {
                    id: idaddress,
                    customer_id,
                    country_id: req.country_id,
                },
            });

            addressEntry = {
                statusCode: 200,
                message: "Address updated successfully!",
                success: "success",
                result: { idaddress },
            };
        } else {
            data.date = currentDateTime(); // Only add `date` for insert

            // console.log(data);
            const created = await CrmCustomerAddress.create(data);
            addressEntry = {
                statusCode: 200,
                message: "Address added successfully!",
                success: "success",
                result: { idaddress: created.id },
            };
        }

        return addressEntry;
    } catch (err) {
        throw err;
    }
};

export const saveDefaultAddressService = async (req) => {
    try {
        const customer_id = req.user?.user_id;
        const { idaddress } = req.body;

        const result = await CrmCustomerAddress.findOne({
            where: { id: idaddress, customer_id },
            // raw: true, remove because instance methods like .update(), .save()
        });

        if (!result?.id) {
            return {
                statusCode: 200,
                message: "Address not found!",
                success: "failure",
                result: {},
            };
        }

        // Reset all addresses for the user
        await CrmCustomerAddress.update(
            { default_address: 0 },
            { where: { customer_id } }
        );

        // Set the selected address as default
        await result.update({ default_address: 1 });

        return {
            statusCode: 200,
            message: "Address has been set default!",
            success: "success",
            result: { id: idaddress },
        };
    } catch (err) {
        throw err;
    }
};

export const deleteUserAddressService = async (req) => {
    try {
        const userId = req.user?.user_id;
        const { idaddress } = req.body;

        if (!idaddress || idaddress <= 0) {
            return "invalid";
        }

        const deletedCount = await CrmCustomerAddress.destroy({
            where: {
                id: idaddress,
                customer_id: userId,
            },
        });

        return deletedCount === 1
            ? {
                  statusCode: 200,
                  message: "Address deleted successfully",
                  success: "success",
                  result: {},
              }
            : {
                  statusCode: 200,
                  message: "Address not found",
                  success: "failure",
                  result: {},
              };
    } catch (err) {
        throw err;
    }
};

export const selectAddressService = async (req) => {
    try {
        const customer_id = req.user?.user_id;
        const { idaddress } = req.body;

        const result = await CrmCustomerAddress.findOne({
            where: { id: idaddress, customer_id },
        });

        if (!result && !result?.id) {
            return {
                statusCode: 200,
                message: "Address not found!",
                success: "failure",
                result: {},
            };
        }

        // Reset all addresses for the user
        await CrmCustomerAddress.update(
            { select_address: 0 },
            { where: { customer_id } }
        );

        // Set the selected address as default
        await result.update({ select_address: 1 });

        return {
            statusCode: 200,
            message: "Address has been set selected!",
            success: "success",
            result: { id: idaddress },
        };
    } catch (err) {
        throw err;
    }
};

export const changeAddressStatusService = async (req) => {
    try {
        const customer_id = req.user?.user_id;
        const { idaddress, select_address = 0 } = req.body;

        const result = await CrmCustomerAddress.findOne({
            where: { id: idaddress, customer_id },
        });

        if (!result && !result?.id) {
            return {
                statusCode: 200,
                message: "Address not found!",
                success: "failure",
                result: {},
            };
        }

        await result.update({ select_address });

        return {
            statusCode: 200,
            message: "Address has been set selected!",
            success: "success",
            result: { id: idaddress },
        };
    } catch (err) {
        throw err;
    }
};
