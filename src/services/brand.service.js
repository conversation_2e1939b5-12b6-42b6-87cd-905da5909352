import {
    getAllFilteredBrandItemsElk,
    getBrandsBySubCategoryElk,
} from "../elk/brand.elk.js";
import CatalogBrand from "../models/catalog_brand.model.js";

import CmsBanners from "../models/cms_banners.model.js";
import { CURRENCY } from "../region/config.js";
import { transformProductItem, getCmsCustomData } from "./common.service.js";
import { col } from "sequelize";

export const getAllBrandItemsService = async (request, fastify) => {
    try {
        const CDN_IMAGE_BASE_URL = process.env.CDN_IMAGE_BASE_URL;

        let { brand_name, brand_id, slug, subcategory_id, category_id } =
            request.query;
        const currency = CURRENCY[request.country_id].currency;
        let elk_result;
        if (brand_id == 0 && slug) {
            const brandResult = await getBrandId(slug);
            brand_name = brandResult?.name;
        }
        elk_result = await getAllFilteredBrandItemsElk(
            request,
            brand_name,
            fastify
        );

        let elk_brands = [];
        let sql_output = [];

        if (subcategory_id) {
            const brands = await getBrandsBySubCategoryElk(
                subcategory_id,
                fastify
            );
            elk_brands = brands.brands;

            sql_output = await getCategoryBannerSQL(
                request.country_id,
                category_id
            );
        } else {
            sql_output = [
                {
                    image_url: `${CDN_IMAGE_BASE_URL}/ourshopee-img/ourshopee_category_sliders/1_14042025110809_page-Mobilefest.jpg`,
                    mobile_image_url: `${CDN_IMAGE_BASE_URL}/ourshopee-img/ourshopee_category_sliders/2_14042025110809_App-Mobilefest.jpg`,
                    id: 1,
                    url: "",
                },
            ];
        }

        // Step 1: Extract & normalize once
        const elk_normalized_data = elk_result.categories.map((item) => {
            const doc = item.docs.hits.hits[0]._source;
            return {
                category_id: doc.category_id,
                value: doc.category_id + "_" + doc.category_id + "@category",
                label: doc.category_name,
                subcategory_id: doc.subcategory_id,
                subcategory_name: doc.subcategory_name,
                sub_sub_category_id: doc.sub_sub_category_id,
                sub_sub_category_name: doc.sub_sub_category_name,
            };
        });

        // Step 2: Remove duplicate categories using a Map for fast lookup
        const uniqueMap = new Map();
        elk_normalized_data.forEach((item) => {
            const key =
                item.category_id +
                "|" +
                item.subcategory_id +
                "|" +
                item.sub_sub_category_id;
            if (!uniqueMap.has(key)) {
                uniqueMap.set(key, item);
            }
        });
        const uniqueData = [...uniqueMap.values()];

        // Step 3: Structure the nested categories/subcategories/sub-subcategories
        const categories = [];
        const categoryMap = new Map();

        for (const item of uniqueData) {
            let category = categoryMap.get(item.category_id);
            if (!category) {
                category = {
                    category_id: item.category_id,
                    value:
                        item.category_id + "_" + item.category_id + "@category",
                    label: item.label,
                    children: [],
                };
                categoryMap.set(item.category_id, category);
                categories.push(category);
            }

            let subcategory = category.children.find(
                (sc) => sc.subcategory_id === item.subcategory_id
            );
            if (!subcategory && item.subcategory_id) {
                subcategory = {
                    category_id: item.category_id,
                    subcategory_id: item.subcategory_id,
                    value:
                        item.subcategory_id +
                        "_" +
                        item.category_id +
                        "@subcategory",
                    label: item.subcategory_name,
                    children: [],
                };
                category.children.push(subcategory);
            }

            if (item.sub_sub_category_id) {
                subcategory.children.push({
                    sub_category_id: item.subcategory_id,
                    value:
                        item.subcategory_id +
                        "_" +
                        item.subcategory_id +
                        "@subsubcategory",
                    sub_subcategory_id: item.sub_sub_category_id,
                    label: item.sub_sub_category_name,
                });
            }
        }

        // Extract colors, avoiding ID = 0
        const colors = elk_result.colors
            .map((ele) => {
                const src = ele.docs.hits.hits[0]?._source;
                return src?.color_id
                    ? { id: src.color_id, name: src.color_name }
                    : null;
            })
            .filter(Boolean); // removes null entries

        // Extract brands
        const brands = elk_brands.map((ele) => {
            const src = ele.docs.hits.hits[0]?._source;
            return {
                brand_id: src?.brand_id || null,
                brand_name: src?.brand_name || "",
                image: src?.brand_image || "",
            };
        });

        // Extract products with calculated display_price and percentage
        const products = elk_result.products.map((item) => {
            return {
                id: item.id,
                brand_id: item.brand_id,
                subcategory_id: item.subcategory_id,
                ...transformProductItem(item, currency),
            };
        });

        // Compose output
        const return_output = {
            filters: {
                slider_range: [
                    {
                        title: "price",
                        min_value: 0,
                        max_value: elk_result.max_price || 0,
                    },
                ],
                checkbox: [
                    {
                        id: Math.floor(Math.random() * 10000),
                        title: "Colors",
                        list: colors,
                    },
                ],
                categories,
            },
            display_items: {
                banners: sql_output,
                top_brands: brands,
                products,
            },
        };

        return return_output;
    } catch (err) {
        throw err;
    }
};

export const getBrandId = async (slug) => {
    const splitSlug = slug.split("/")[0];
    try {
        const brand = await CatalogBrand.findOne({
            where: { slug: splitSlug },
            attributes: [[col("brand_name"), "name"]],
            raw: true,
        });
        return brand || null;
    } catch (err) {
        throw err;
    }
};

export const getCategoryBannerService = async (countryId, category_id) => {
    try {
        // CategorySlider
        const sliders = await CmsBanners.findAll({
            where: {
                country_id: countryId,
                type_id: category_id,
                rstatus: 1,
                type: 6,
            },
            order: [["position", "ASC"]],
            limit: 5,
            attributes: ["id", "banner_images"],
            raw: true,
            // logging: console.log,
        });
        // console.log("sliders", sliders);

        return sliders.map((row) => {
            const data = getCmsCustomData(row.banner_images, row.type);

            return {
                id: row.id,
                url: data.url ? data.url : "",
                image_url: data.image ? data.image : "",
                mobile_image_url: data.mobile_image ? data.mobile_image : "",
            };
        });
    } catch (err) {
        throw err;
    }
};

export const getCategoryBannerSQL = async (country_id, category_id) => {
    try {
        // CategorySlider
        const sliders = await CmsBanners.findAll({
            where: {
                ...(category_id && { type_id: category_id }),
                status: 1,
                country_id,
                type: 6,
            },
            order: [["position", "ASC"]],
            limit: 5,
            attributes: ["id", "banner_images"],
            raw: true,
        });

        return sliders.map((row) => {
            const data = getCmsCustomData(row.banner_images, row.type);

            return {
                id: row.id,
                url: data.url ? data.url : "",
                image_url: data.image ? data.image : "",
                mobile_image_url: data.mobile_image ? data.mobile_image : "",
            };
        });
    } catch (error) {
        console.error(" Error fetching category banners:", error.message);
        throw error;
    }
};
