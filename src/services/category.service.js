import db from "../models/index.js";
import CatalogBrand from "../models/catalog_brand.model.js";
import CatalogCategory from "../models/catalog_category.model.js";
import fastify from "../app.js";

import { getCategoryBannerService } from "./brand.service.js";
import {
    productCategoryElk,
    searchProductsByCategory,
} from "../elk/category.elk.js";
import { transformProductItem, priceCalculator } from "./common.service.js";

import { CURRENCY } from "../region/config.js";

export const categories_elk = async () => {
    const client = fastify.elasticsearch;
    const result = await client.search({
        index: "categories",
        body: {
            size: "1000",
        },
    });
    return result.hits.hits.map((h) => h._source);
};

export const categorymetatags = async (req) => {
    try {
        const { cat_url: slug } = req.query;
        const result = await CatalogCategory.findOne({
            attributes: ["seo_title", "seo_description", "seo_keywords"],
            where: { slug },
            raw: true,
        });
        return result || {};
    } catch (err) {
        throw err;
    }
};

export const subcategoryMetatagsService = async (req) => {
    try {
        const { url: slug } = req.body;
        const result = await CatalogCategory.findOne({
            attributes: ["seo_title", "seo_description", "seo_keywords"],
            where: { slug },
            raw: true,
        });
        return result || {};
    } catch (err) {
        throw err;
    }
};

export const subsubcategoryMetatagsService = async (req) => {
    try {
        const { url: slug } = req.body;
        const result = await CatalogCategory.findOne({
            attributes: ["seo_title", "seo_description", "seo_keywords"],
            where: { slug },
            raw: true,
        });
        return result || {};
    } catch (err) {
        throw err;
    }
};

export const brandsMetatagsService = async (req) => {
    try {
        const { url: slug } = req.body;
        const result = await CatalogBrand.findOne({
            attributes: [
                "seo_title",
                "seo_description",
                "seo_keywords",
                ["slug", "url"],
            ],
            where: { slug },
            raw: true,
        });
        return result || {};
    } catch (err) {
        throw err;
    }
};

export const getAllCategoryListService = async () => {
    try {
        const categories = await db.CatalogCategory.findAll({
            where: {
                categorypid: 0,
                rstatus: 1,
            },
            order: [["position", "ASC"]],
            attributes: [
                "categoryid",
                "category_name",
                "slug",
                "category_images",
                "position",
            ],
            include: [
                {
                    model: db.CatalogCategory,
                    as: "subcategories",
                    required: false,
                    where: { rstatus: 1 },
                    attributes: [
                        "categoryid",
                        "category_name",
                        "slug",
                        "category_images",
                        "position",
                        "categorypid",
                    ],
                    order: [["position", "ASC"]],
                    include: [
                        {
                            model: db.CatalogCategory,
                            as: "subcategories",
                            required: false,
                            where: { rstatus: 1 },
                            attributes: [
                                "categoryid",
                                "category_name",
                                "slug",
                                "category_images",
                                "position",
                                "categorypid",
                            ],
                        },
                    ],
                },
            ],
        });

        const output = categories.map((category) => {
            return {
                category_id: category.categoryid,
                category_name: category.category_name,
                url: category.slug,
                subcategory: (category.subcategories || []).map((sub) => {
                    return {
                        category_id: category.categoryid,
                        sub_category_id: sub.categoryid,
                        sub_category_name: sub.category_name,
                        url: sub.slug,
                        sub_subcategory: (sub.subcategories || []).map(
                            (subsub) => {
                                return {
                                    sub_category_id: sub.categoryid,
                                    sub_subcategory_id: subsub.categoryid,
                                    sub_subcategory_name: subsub.category_name,
                                    url: `${sub.slug}/${subsub.slug}`,
                                };
                            }
                        ),
                    };
                }),
            };
        });

        return output;
    } catch (err) {
        throw err;
    }
};

export const getAllItemsService = async (req) => {
    try {
        const countryId = req.country_id;

        const result = await searchProductsByCategory(req);
        const currency = CURRENCY?.[countryId]?.currency || "AED";

        const products = result.hits.hits.map((hit) => {
            const item = hit._source;
            const inventory = hit.inner_hits.inventory_hit.hits.hits[0]._source;
            const cleanedItem = { ...item, ...inventory };
            return transformProductItem(cleanedItem, currency);
        });

        return products;
    } catch (err) {
        throw err;
    }
};

const extractBrands = (category, allBrands) => {
    if (category.brand_detail && category.brand_detail.length > 0) {
        category.brand_detail.forEach((brand) => {
            if (brand.brandid && !allBrands.has(brand.brandid)) {
                allBrands.set(brand.brandid, brand);
            }
        });
    }
    if (category.childCategory && category.childCategory.length > 0) {
        category.childCategory.forEach((child) =>
            extractBrands(child, allBrands)
        );
    }
};

const extractCategories = (category, allCategories) => {
    if (category.category_name) {
        allCategories.push({
            subcategory_id: category.categoryid,
            subcategory_name: category.category_name,
            url: category.slug,
            items: [],
        });
    }
    if (category.childCategory && category.childCategory.length > 0) {
        category.childCategory.forEach((child) =>
            extractCategories(child, allCategories)
        );
    }
};

export const getAllCategoryItemsService = async (req) => {
    const currency = CURRENCY[req.country_id].currency;
    const countryId = req.country_id;
    const category_output = await productCategoryElk(req);
    const CDN_IMAGE_BASE_URL = process.env.CDN_IMAGE_BASE_URL;

    const meta_tags = await categorymetatags(req);
    const top_brands = category_output.top_brands.map((ele) => {
        const brandData = ele.docs.hits.hits[0]._source;
        let brand_image = brandData.brand_image;

        if (brand_image) {
            const splitted = brand_image.split("ourshopee_brands");

            if (splitted.length > 1) {
                const afterSplit = splitted[1];

                if (
                    afterSplit !== "/" &&
                    afterSplit.includes(".") &&
                    afterSplit.split(".")[1]
                ) {
                    return {
                        id: brandData.brand_id,
                        brand_name: brandData.brand_name,
                        url: brandData.brand_url,
                        image: CDN_IMAGE_BASE_URL + brand_image,
                    };
                }
            }
        }
    });

    const typeId =
        category_output?.product_categories?.[0]?.docs?.hits?.hits?.[0]?._source
            ?.category_id || 1;

    let sql_output = await getCategoryBannerService(countryId, typeId);

    const categories = category_output.product_categories
        .map((ele) => {
            const topProduct = ele.docs.hits.hits[0];

            if (!topProduct || !topProduct._source) return null;

            const subcategory_name = topProduct._source.subcategory_name;

            // Skip "Other" subcategory if category_id is 97
            // if (
            //     (sql_output && sql_output.length === 0) ||
            //     (sql_output?.[0]?.id === 97 && subcategory_name === 'Other')
            // ) {
            //     return null;
            // }

            if (subcategory_name !== undefined) {
                return {
                    subcategory_id: topProduct._source.subcategory_id,
                    subcategory_name: subcategory_name,
                    url: topProduct._source.subcategory_url,
                    items: ele.docs.hits.hits.map((productHit) => {
                        const product = productHit._source;
                        const inventory =
                            productHit.inner_hits?.inventory?.hits?.hits?.[0]
                                ?._source || {};

                        return transformProductItem(
                            { ...product, ...inventory },
                            currency
                        );
                    }),
                };
            }

            return null;
        })
        .filter(Boolean);

    try {
        const return_output = {
            category_image: sql_output,
            hot_deals: category_output.hot_deals.hits.map((productHit) => {
                const product = productHit._source;
                const inventory =
                    productHit.inner_hits?.inventory?.hits?.hits?.[0]
                        ?._source || {};

                return transformProductItem(
                    { ...product, ...inventory },
                    currency
                );
            }),
            top_brands: top_brands.filter((ele) => ele != null).slice(0, 16),
            categories: categories
                .reduce(function (si, current) {
                    return si.concat(current);
                }, [])
                .filter((ele) => ele != null),
            meta_tags: meta_tags,
        };
        return return_output;
    } catch (err) {
        return "error";
    }
};
