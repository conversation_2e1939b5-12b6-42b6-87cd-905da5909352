import AdminNationality from "../models/admin_nationalities.model.js";
import AdminCountryArea from "../models/admin_country_area.model.js";
import AdminCountryEmirates from "../models/admin_country_emirates.model.js";

export const GetNationalityService = async () => {
    try {
        const result = await AdminNationality.findAll({
            attributes: ["id", "country_name"],
            raw: true,
        });

        if (result.length === 0) {
            return {
                statusCode: 200,
                message: "Data not found!",
                status: "notfound",
                data: [],
            };
        }

        const mapped = result.map((data) => ({
            value: data.id,
            label: data.country_name,
        }));

        return {
            statusCode: 200,
            message: "Data fetched successfully!",
            status: "success",
            data: mapped,
        };
    } catch (err) {
        throw err;
    }
};

export const getLocationsService = async (country_id, query = {}) => {
    try {
        const where = {
            country_id,
        };

        if (query.id && Number(query.id) > 0) {
            where.emirateid = Number(query.id);
        }

        const result = await AdminCountryEmirates.findAll({
            attributes: [
                ["emirateid", "id"],
                ["emirate", "name"],
            ],
            where,
            raw: true,
        });

        if (result.length === 0) {
            return {
                status: "notfound",
                statusCode: 200,
                message: "No locations found",
                data: [],
            };
        }

        return {
            status: "success",
            statusCode: 200,
            message: "Locations fetched successfully",
            data: result,
        };
    } catch (err) {
        throw err;
    }
};

export const getAreasService = async (country_id, query = {}) => {
    try {
        // emirateid is required
        if (!query.emirateid || Number(query.emirateid) === 0) {
            return {
                status: "error",
                statusCode: 200,
                message: "Missing or invalid emirateid",
                data: [],
            };
        }

        const where = {
            emirateid: query.emirateid.toString(),
        };

        if (query.id && Number(query.id) > 0) {
            where.area_id = Number(query.id);
        }

        const result = await AdminCountryArea.findAll({
            attributes: ["emirateid", ["area_id", "id"], "name"],
            where,
            raw: true,
        });

        if (result.length > 0) {
            return {
                status: "success",
                statusCode: 200,
                message: "Area data fetched",
                data: result,
            };
        }

        return {
            status: "success",
            statusCode: 200,
            message: "Area not found, returning default",
            data: [{ id: 0, name: "other" }],
        };
    } catch (err) {
        throw err;
    }
};
