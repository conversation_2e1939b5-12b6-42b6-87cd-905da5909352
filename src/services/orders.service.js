// --- Coupon validation logic moved from promotion.service.js ---
// import moment from "moment-timezone";
import CatalogPromotion from "../models/catalog_promotion.model.js";
import CrmCustomer from "../models/crm_customer.model.js";
import {
    CustComplaint,
    CustComplaintStatus,
} from "../models/crm_ticket.model.js";
import OmsOrders from "../models/oms_orders.model.js";
import OmsOrderDetail from "../models/oms_order_detail.model.js";
import OmsOrderHistory from "../models/oms_order_history.model.js";
import CatalogProduct from "../models/catalog_product.model.js";
import OmsWebfeed from "../models/oms_webfeed.model.js";
import AdminLists from "../models/admin_list.model.js";
import { currentDateTime } from "../utils/dateTimeHandler.js";
import { Op } from "sequelize";
import moment from "moment";
import { addZeroes, formatOrderDate } from "./common.service.js";
import AauthUsers from "../models/aauth_users.model.js";
import { ORDER_FROM_ID } from "../region/config.js";

/**
 * Validates a coupon code and calculates discount
 * @param {Object} req - Request object containing coupon details
 * @returns {Object} - Validation result with message and discount
 */
export const checkCouponCode = async (req) => {
    try {
        const { coupon, tamount, offer = 0 } = req.body;
        const userId = req.user?.user_id;
        const countryId = req.country_id || 6;

        if (!userId) {
            return {
                msg: "User authentication required",
                discount: 0,
                link: "",
            };
        }

        // Convert tamount to number
        const totalAmount = parseFloat(tamount);
        if (isNaN(totalAmount) || totalAmount <= 0) {
            return {
                msg: "Invalid total amount",
                discount: 0,
                link: "",
            };
        }

        // Get current date/time in Dubai timezone
        const now = moment().tz("Asia/Dubai");
        const dateStringWithTime = now.format("YYYY-MM-DD HH:mm:ss");

        // Get user details from crm_customer table
        const userDetails = await CrmCustomer.findByPk(userId, {
            attributes: ["mobile"],
            raw: true,
        });
        const userMobile = userDetails?.mobile || "";

        // Find active promotion for the coupon using catalog_promotion model
        const couponDetails = await CatalogPromotion.findOne({
            where: {
                promo_code: coupon,
                startdate: { [Op.lt]: dateStringWithTime },
                enddate: { [Op.gt]: dateStringWithTime },
                channel_type: { [Op.in]: [0, 2] }, // web or both platforms
            },
            raw: true,
        });

        let promotionType = "";
        let minAmount = 0;
        let couponValue = 0;
        let maxUse = 0;

        if (couponDetails) {
            promotionType = couponDetails.promo_type;
            minAmount = parseFloat(couponDetails.min_purchase_amount) || 0;
            couponValue = parseFloat(couponDetails.discount_value) || 0;
            maxUse = couponDetails.max_usa_user || 0;
        }

        // Check if user registered with this promotion code using crm_customer
        const userWithPromoCode = await CrmCustomer.findOne({
            where: { id: userId },
            attributes: ["id"],
            raw: true,
        });

        let msg = "";
        let discount = 0;
        let link = "";

        // Check offer product minimum amount requirement
        if (offer > 0 && totalAmount < 200) {
            return {
                msg: "To Redeem Voucher code on Offer products, Minimum purchase amount should be BHD 200 or Above !",
                discount: 0,
                link: "",
            };
        }

        // Validate coupon existence and eligibility
        if (couponDetails || userWithPromoCode) {
            const validationResult = await validateCouponEligibility({
                couponDetails,
                userWithPromoCode,
                promotionType,
                userId,
                userMobile,
                tamount: totalAmount,
                minAmount,
                coupon,
                maxUse,
                couponValue,
                dateStringWithTime,
            });
            msg = validationResult.msg;
            discount = validationResult.discount;
        } else {
            // Check if coupon is for mobile app only
            const mobileOnlyCoupon = await CatalogPromotion.findOne({
                where: {
                    promo_code: coupon,
                    startdate: { [Op.lt]: dateStringWithTime },
                    enddate: { [Op.gt]: dateStringWithTime },
                    channel_type: 1, // mobile app only
                },
                raw: true,
            });
            if (mobileOnlyCoupon) {
                msg = "Coupon applicable only on mobile app !";
                // link = "http://www.ourshopee.org/mob-app"; // currently not using this url for mobile app
            } else {
                msg = "Invalid Coupon Code";
            }
        }
        return { msg, discount, link };
    } catch (error) {
        throw error;
    }
};

const validateCouponEligibility = async (params) => {
    const {
        couponDetails,
        userWithPromoCode,
        promotionType,
        userId,
        userMobile,
        tamount,
        minAmount,
        coupon,
        maxUse,
        couponValue,
    } = params;
    let msg = "";
    let discount = 0;

    // User-specific coupon validation
    if (
        promotionType == 4 &&
        couponDetails?.customerid != userId &&
        couponDetails?.customerid != 0
    ) {
        msg = "Invalid Coupon Code.";
        return { msg, discount };
    }

    // Mobile-specific coupon validation
    if (
        promotionType == 3 &&
        couponDetails?.mobile_no?.trim() != userMobile?.trim() &&
        couponDetails?.customerid == 0
    ) {
        msg = "Invalid Coupon Code.";
        return { msg, discount };
    }

    // Mobile + user specific validation
    if (
        promotionType == 4 &&
        couponDetails?.mobile_no?.trim() != userMobile?.trim() &&
        couponDetails?.customerid == 0
    ) {
        msg = "Invalid Coupon Code";
        return { msg, discount };
    }

    // Limited use coupon (type 6)
    if (promotionType == 6 && tamount >= minAmount) {
        if (maxUse > 0) {
            discount = couponValue / maxUse;
        }
        return { msg, discount };
    }

    // Minimum amount validation
    if (tamount < minAmount && promotionType != 3) {
        msg = `Voucher code valid only on purchase of BHD ${minAmount} or above.`;
        return { msg, discount };
    }

    // Calculate discount based on promotion type
    switch (parseInt(promotionType)) {
        case 5: // Brand-specific percentage discount
        case 7: // Category-specific percentage discount
        case 8: // Sub-category-specific percentage discount
        case 9: // Sub-sub-category-specific percentage discount
        case 10: // Sub-category + brand-specific percentage discount
            if (tamount > minAmount) {
                discount = calculateCategoryBrandDiscount(
                    promotionType,
                    couponDetails,
                    tamount
                );
            }
            break;
        default:
            // Fixed value discount
            if (couponDetails?.promo_code) {
                discount = couponValue;
            }
            if (userWithPromoCode?.promotion_code) {
                discount = couponDetails?.discount_value || 0;
            }
            break;
    }
    return { msg, discount };
};

const calculateCategoryBrandDiscount = (
    promotionType,
    couponDetails,
    totalAmount
) => {
    const percentage = parseFloat(couponDetails.deduction_percent) || 0;
    const maxDiscount = parseFloat(couponDetails.deduction_amount) || 0;
    let discount = (percentage / 100) * totalAmount;
    if (maxDiscount > 0 && discount > maxDiscount) {
        discount = maxDiscount;
    }
    return discount;
};

export const getAvailablePromotions = async (req) => {
    try {
        const userId = req.user?.user_id;
        const countryId = req.country_id || 6; // Default to Bahrain if not provided

        const now = moment().tz("Asia/Dubai").format("YYYY-MM-DD HH:mm:ss");
        const promotions = await CatalogPromotion.findAll({
            where: {
                startdate: { [Op.lt]: now },
                enddate: { [Op.gt]: now },
                channel_type: { [Op.in]: [0, 2] },
                country_id: countryId,
            },
            attributes: [
                "promotionid",
                "promo_code",
                "promo_type",
                "deduction_percent",
                "deduction_amount",
                "start_date",
                "end_date",
            ],
            raw: true,
        });
        return promotions;
    } catch (error) {
        throw error;
    }
};

export const addComplaintServiceOld = async (req) => {
    try {
        const { invoice, email, mobile, comment } = req.body;
        const country_id = req.country_id;

        console.log("user", req.user);
        // Validate required fields
        if (!invoice) {
            return {
                statusCode: 400,
                message: "Invoice number is required",
                status: false,
                data: {},
            };
        }

        // Find orderid from oms_orders using invoice value
        const orderRecord = await OmsOrders.findOne({
            where: {
                [Op.or]: [
                    { order_ref_code: invoice },
                    { invoice_no: invoice }, // Replace with your actual field and value
                ],
            },
            attributes: ["orderid", "customer_details"],
            raw: true,
        });

        if (!orderRecord) {
            return {
                statusCode: 404,
                message: "Order not found",
                status: false,
                data: {},
            };
        }

        // Parse customer_details JSON string before accessing properties
        let customerDetailsObj = {};
        if (orderRecord.customer_details) {
            if (typeof orderRecord.customer_details === "string") {
                try {
                    customerDetailsObj = JSON.parse(
                        orderRecord.customer_details
                    );
                } catch (e) {
                    customerDetailsObj = {};
                }
            } else {
                customerDetailsObj = orderRecord.customer_details;
            }
        }

        // Check for existing complaint by orderid
        const existingComplaint = await CustComplaint.findOne({
            where: { ticket_id: orderRecord.orderid.toString() },
        });

        if (existingComplaint) {
            return {
                statusCode: 409,
                message: "Complaint already registered for this order",
                status: false,
                data: [],
            };
        }

        const customerName = customerDetailsObj?.name || email || "Customer";

        // Create new complaint
        const newComplaint = await CustComplaint.create({
            ticket_id: orderRecord.orderid.toString(),
            customer_name: customerName,
            contact: mobile || "N/A",
            rstatus: "Pending",
        });

        // Add initial comment if provided
        if (comment && comment.trim()) {
            try {
                await CustComplaintStatus.create({
                    ticket_id: newComplaint.ticket_id,
                    comment: comment.trim(),
                    comment_date: new Date(),
                });
            } catch (commentError) {
                // Don't fail the whole operation if comment fails
            }
        }

        if (newComplaint && newComplaint.ticket_id) {
            const responseData = {
                ticket_id: newComplaint.ticket_id,
                customer_name: newComplaint.customer_name,
                contact: newComplaint.contact,
                rstatus: newComplaint.rstatus,
            };
            return {
                statusCode: 200,
                message: "Complaint successfully registered",
                status: true,
                data: [responseData],
            };
        } else {
            return {
                statusCode: 500,
                message: "Error registering complaint",
                status: false,
                data: [],
            };
        }
    } catch (err) {
        throw err;
    }
};

export const addComplaintService = async (req) => {
    try {
        const current_date = currentDateTime();
        const { invoice, email, mobile, comment } = req.body;
        const countryId = req.country_id;

        if (!invoice || !mobile || !comment) {
            return {
                status: false,
                message: "Missing required fields: invoice, mobile, comment",
                data: {},
            };
        }

        // Check for existing open complaint
        const existingComplaint = await CustComplaint.findOne({
            where: {
                rstatus: 1,
                [Op.or]: [{ orderno: invoice }],
                resolved_date: null, // assuming unresolved tickets
            },
        });

        if (existingComplaint) {
            return {
                status: false,
                message: `Your complaint is already in progress. We will update you soon.`,
                data: {},
            };
        }

        // Check order existence
        const order = await OmsOrders.findOne({
            where: {
                [Op.or]: [{ invoice_no: invoice }, { order_ref_code: invoice }],
                // customer_contact: mobile,
            },
        });

        if (!order) {
            return {
                status: false,
                message:
                    "Please enter a valid Order ID / Registered contact number.",
                data: {},
            };
        }

        // Parse customer_details JSON string before accessing properties
        let customerDetailsObj = {};
        if (order.customer_details) {
            if (typeof order.customer_details === "string") {
                try {
                    customerDetailsObj = JSON.parse(order.customer_details);
                } catch (e) {
                    customerDetailsObj = {};
                }
            } else {
                customerDetailsObj = order.customer_details;
            }
        }

        const customerName = customerDetailsObj?.name || email || "Customer";

        const complaint_id = "OS" + Date.now() + invoice;

        const newTicket = await CustComplaint.create({
            ticket_source: "web",
            country_id: countryId,
            complaint_id,
            ticket_categoryid: 1,
            ticket_type: 1,
            title: `Complaint for Order ${order.orderid}`,
            description: comment,
            proirity: 1,
            orderno: order.order_ref_code,
            customerid: order.customerid || null,
            customer_name: customerName,
            contact: mobile,
            created_date: current_date,
            created_by: 0,
            // assign_togroup: 19,
            rstatus: 1,
            reopen: 0,
        });

        if (newTicket?.ticket_id) {
            return {
                status: true,
                message: `Your complaint has been registered successfully. Your Complaint Ref No is ${complaint_id}`,
                data: { complaint_id: complaint_id },
            };
        } else {
            return {
                status: false,
                message: "Network error, try again please.",
                data: {},
            };
        }
    } catch (err) {
        console.error("Complaint error:", err);
        throw err;
    }
};

export const getComplaintService = async (req) => {
    try {
        const { cno } = req.body;
        const data = await CustComplaint.findOne({
            where: {
                [Op.or]: [{ orderno: cno }, { complaint_id: cno }],
            },
            raw: true,
        });
        if (data) {
            let msg = `Hi, ${data.customer_name}\nThank you for contacting OurShopee Support\nYour complaint reference number is ${data.complaint_id}`;
            let msgType = 1;
            if (data.rstatus == 1) {
                msg = `Your complaint reference number <b>'${data.complaint_id}'</b> is in Progress.`;
                msgType = 2;
            }
            // Fetch comments if needed
            const comments = await CustComplaintStatus.findAll({
                where: { ticket_id: data.ticket_id }, // Link comments by ticket_id
                raw: true,
            });
            return {
                statusCode: 200,
                message: "Data fetched successfully",
                status: true,
                data: {
                    name: data.customer_name,
                    msg,
                    msgType,
                    complaintId: data.complaint_id,
                    comments,
                },
            };
        } else {
            return {
                statusCode: 200,
                message:
                    "Invalid Complain ID. Please check your COMPLAINT REFERENCE NUMBER again",
                status: true,
                data: {
                    msgType: 11,
                },
            };
        }
    } catch (err) {
        throw err;
    }
};

export const myOrdersService = async (req) => {
    try {
        const CDN_IMAGE_BASE_URL = process.env.CDN_IMAGE_BASE_URL;
        const userId = req.user?.user_id;
        const countryId = req.country_id;
        console.log("userId", userId, "countryId", countryId);
        const page = parseInt(req.query.page) || 1;
        const offset = page === 1 ? 0 : (page - 1) * 10;

        const whereCondition = {
            customerid: userId,
            order_statusid: { [Op.ne]: 0 },
            country_id: countryId,
        };

        // Apply refid filter if present in params
        if (req.query?.refid) {
            whereCondition.order_ref_code = req.query.refid;
        }

        // console.log('whereCondition', whereCondition)
        const orders = await OmsOrders.findAll({
            where: whereCondition,
            order: [["orderid", "DESC"]],
            limit: 10,
            offset,
            raw: true,
        });

        if (orders.length === 0) {
            return {
                statusCode: 200,
                message: "No orders found",
                status: true,
                data: [],
            };
        }

        // Get order IDs for related queries
        const orderIds = orders.map((order) => order.orderid);

        // Get order details (products)
        const orderDetails = await OmsOrderDetail.findAll({
            where: {
                orderid: { [Op.in]: orderIds },
            },
            raw: true,
        });

        // Get product details separately
        const productIds = orderDetails
            .map((detail) => detail.productid)
            .filter(Boolean);
        let products = [];

        if (productIds.length > 0) {
            try {
                products = await CatalogProduct.findAll({
                    where: {
                        productid: { [Op.in]: productIds },
                    },
                    raw: true,
                });
            } catch (error) {
                // If catalog_product table doesn't exist, continue with empty array
                products = [];
            }
        }

        // Get order history for status tracking
        const orderHistory = await OmsOrderHistory.findAll({
            where: {
                orderid: { [Op.in]: orderIds },
            },
            order: [["statusdate", "ASC"]],
            raw: true,
        });

        // Process and format the results
        const invoiceBaseUrl =
            process.env.INVOICE_BASE_URL || "https://www.ourshopee.com";
        const formattedOrders = orders.map((order) => {
            // Parse customer_details JSON
            let customerDetails = {};
            if (typeof order.customer_details === "string") {
                try {
                    customerDetails = JSON.parse(order.customer_details);
                } catch (e) {
                    customerDetails = {};
                }
            } else if (order.customer_details) {
                customerDetails = order.customer_details;
            }

            // Parse shipping address JSON
            let shippingAddress = {};
            if (typeof order.shippingaddress === "string") {
                try {
                    shippingAddress = JSON.parse(order.shippingaddress);
                } catch (e) {
                    shippingAddress = {};
                }
            } else if (order.shippingaddress) {
                shippingAddress = order.shippingaddress;
            }

            // Date calculations
            const ordered_date = formatOrderDate(order.order_date);
            const packed_date = formatOrderDate(order.order_date, 2);
            const dispatched_date = formatOrderDate(order.order_date, 3);
            const delivery_date = formatOrderDate(order.order_date, 5);

            // Get order history for this specific order
            const thisOrderHistory = orderHistory.filter(
                (history) => history.orderid === order.orderid
            );

            // Determine order status flags based on order history
            let process_date_over = false;
            let pack_date_over = false;
            let dispatch_date_over = false;
            let delivery_date_over = false;
            let cancelledFlag = false;
            let cancelledDate = "";
            let invoiceLink = "";

            // Check if order is cancelled
            if (
                order.order_statusid === 4 ||
                order.order_status === "Cancelled"
            ) {
                cancelledFlag = true;
                const cancelHistory = thisOrderHistory.find(
                    (h) => h.order_statusid === 4
                );
                if (cancelHistory) {
                    cancelledDate = moment(cancelHistory.statusdate).format(
                        "ddd, MMM Do, h:mm A"
                    );
                }
            }

            // Check order progress based on history
            if (thisOrderHistory.length > 0) {
                const processedHistory = thisOrderHistory.find(
                    (h) => h.order_statusid === 2
                );
                const packedHistory = thisOrderHistory.find(
                    (h) => h.order_statusid === 3
                );
                const dispatchedHistory = thisOrderHistory.find(
                    (h) => h.order_statusid === 5
                );
                const deliveredHistory = thisOrderHistory.find(
                    (h) => h.order_statusid === 6
                );

                if (processedHistory) process_date_over = true;
                if (packedHistory) pack_date_over = true;
                if (dispatchedHistory) dispatch_date_over = true;
                if (deliveredHistory) {
                    delivery_date_over = true;
                    invoiceLink = `${invoiceBaseUrl}order_id=${order.orderid}&refid=${order.order_ref_code}`;
                }
            }

            // Get order items
            const items = orderDetails
                .filter((detail) => detail.orderid === order.orderid)
                .map((item) => {
                    // Find corresponding product
                    const product = products.find(
                        (p) => p.productid === item.productid
                    );

                    let productName = `Product ${item.productid}`;
                    let productImage = "default-product.jpg";

                    if (product) {
                        productName = product.title || productName;

                        if (product.product_images) {
                            try {
                                const parsedImages =
                                    typeof product.product_images === "string"
                                        ? JSON.parse(product.product_images)
                                        : product.product_images;

                                // Extract image URL from the nested structure
                                let imageUrl = null;

                                if (
                                    parsedImages.image &&
                                    Array.isArray(parsedImages.image) &&
                                    parsedImages.image.length > 0
                                ) {
                                    imageUrl =
                                        parsedImages.image[0].file_url ||
                                        parsedImages.image[0].original;
                                } else if (
                                    parsedImages.web_images &&
                                    Array.isArray(parsedImages.web_images) &&
                                    parsedImages.web_images.length > 0
                                ) {
                                    imageUrl =
                                        parsedImages.web_images[0].file_url ||
                                        parsedImages.web_images[0].original;
                                } else if (
                                    Array.isArray(parsedImages) &&
                                    parsedImages.length > 0
                                ) {
                                    imageUrl = parsedImages[0];
                                }

                                if (imageUrl) {
                                    const cleanImageUrl = imageUrl.startsWith(
                                        "/"
                                    )
                                        ? imageUrl.substring(1)
                                        : imageUrl;
                                    productImage = `${CDN_IMAGE_BASE_URL}/${cleanImageUrl}`;
                                }
                            } catch (e) {
                                // Continue with default image if parsing fails
                            }
                        }
                    }

                    const itemQuantity = item.quantity || 1;

                    return {
                        name: productName,
                        image: productImage,
                        product_id: item.productid,
                        // amount: item.amount || 0,
                        price: item.selling_price || 0,
                        quantity: itemQuantity,
                    };
                });

            return {
                referenceNo: order.order_ref_code,
                orderId: order.orderid,
                totalAmount: parseInt(order.total_amount) || 0,
                // totalAmount: order.total_amount || 0,
                placedOn: moment(order.order_date).format("YYYY-MM-DD h:mm A"),
                items: items,
                cancelled: cancelledFlag,
                ...(cancelledFlag && {
                    cancelledText:
                        order.payment_status === "Failed"
                            ? "Due to online transaction error, your item has been cancelled"
                            : "As per your request, your item has been cancelled",
                }),
                ...(cancelledFlag && cancelledDate && { cancelledDate }),
                processDateOver: process_date_over,
                packDateOver: pack_date_over,
                dispatchDateOver: dispatch_date_over,
                deliverydateOver: delivery_date_over,
                invoice: invoiceLink ? invoiceLink : "",
                orderDetail: {
                    orderid: order.orderid,
                    order_refid: order.order_ref_code,
                    user_name: customerDetails?.name || "Customer",
                    mobile:
                        customerDetails?.mobile ||
                        customerDetails?.phone ||
                        "N/A",
                    paymode: order.payment_method || "N/A",
                    total_amount: parseInt(order.total_amount) || 0,
                    shipping_charge: order.shipping_charges || 0,
                    processing_fee: addZeroes(order.processing_fee) || "0.00",
                    discount: addZeroes(order.discount_amount) || "0.00",
                    order_date: order.order_date,
                    order_status: order.payment_status || "N/A",
                    shipping_address:
                        shippingAddress?.address ||
                        (typeof shippingAddress === "object"
                            ? JSON.stringify(shippingAddress)
                            : shippingAddress || "N/A"),
                    vat: addZeroes(order.tax_amount) || "0.00",
                    orderedDate: ordered_date,
                    processedDate: ordered_date,
                    packedDate: packed_date,
                    dispatchedDate: dispatched_date,
                    deliveryDate: delivery_date,
                },
            };
        });

        return {
            statusCode: 200,
            message: "Orders fetched successfully",
            status: true,
            data: formattedOrders,
        };
    } catch (err) {
        throw err;
    }
};

export const addFeedServiceOMSORDER = async (req) => {
    try {
        const currentDate = currentDateTime();
        const countryId = req.country_id;

        if (!countryId) {
            return {
                statusCode: 400,
                message: "Country ID is required",
                status: false,
                data: [],
            };
        }

        const {
            form_name,
            contact_no,
            product,
            emirate,
            area,
            delivery_address,
            orderfrom,
            price,
            quantity,
            post,
        } = req.body;

        // Validate required fields
        if (!form_name || !contact_no || !delivery_address || !price) {
            return {
                statusCode: 400,
                message:
                    "Required fields missing: form_name, contact_no, delivery_address, price",
                status: false,
                data: [],
            };
        }

        const post_date = currentDate;

        // Prepare customer_details JSON object (without product)
        const customer_details = {
            name: form_name,
            contact: contact_no,
            address: delivery_address,
            emirate: emirate || null,
            area: area || null,
        };

        // Prepare product_details value for oms_order_detail (only product_name)
        const product_details = product || "Feed Product";

        // Create new order/feed entry in oms_orders table
        const newFeed = await OmsOrders.create({
            customer_details: customer_details,
            order_date: post_date,
            credit_amount: parseInt(price) || 0,
            orderfrom: orderfrom || "feed",
            order_statusid: 1, // Default status
            country_id: parseInt(countryId),
            payment_status: "Due", // Changed from "Pending" to "Due"
            order_status: "Pending", // Changed from "New Order" to "Pending"
        });

        // Create corresponding entry in oms_order_detail table
        if (newFeed && newFeed.orderid) {
            try {
                const orderDetail = await OmsOrderDetail.create({
                    orderid: newFeed.orderid,
                    productid: null, // No specific product ID for feed entries
                    product_details: product_details,
                    quantity: 1, // Default quantity
                    selling_price: parseInt(price) || 0, // Same as price for feed
                    country_id: parseInt(countryId),
                    orderfrom: orderfrom || "feed",
                    order_status: "Pending",
                });
            } catch (detailError) {
                // Continue execution even if order detail creation fails
            }
        }

        if (newFeed && newFeed.orderid) {
            console.log(
                "Feed successfully added with orderid:",
                newFeed.orderid
            );

            const responseData = {
                orderid: newFeed.orderid,
                customer_name: form_name,
                contact_no: contact_no,
                total_amount: price,
                order_date: post_date,
                status: "Pending",
                payment_status: "Due",
            };

            return {
                statusCode: 200,
                message: "Feed successfully added",
                status: true,
                data: [responseData],
            };
        } else {
            console.log("Error adding feed, newFeed:", newFeed);
            return {
                statusCode: 500,
                message: "Error adding feed",
                status: false,
                dataAnd: [],
            };
        }
    } catch (err) {
        throw err;
    }
};

export const addFeedService = async (req) => {
    try {
        const currentDate = currentDateTime();
        const countryId = req.country_id;
        const orderFrom = ORDER_FROM_ID[req.body.orderfrom];

        if (!countryId) {
            return {
                statusCode: 400,
                message: "Country ID is required",
                status: false,
                data: [],
            };
        }

        const {
            form_name,
            contact_no,
            product,
            emirate,
            area,
            delivery_address,

            price,
            quantity,
            post,
        } = req.body;

        // Validate required fields
        if (!form_name || !contact_no || !delivery_address || !price) {
            return {
                statusCode: 400,
                message:
                    "Required fields missing: form_name, contact_no, delivery_address, price",
                status: false,
                data: [],
            };
        }

        const webFeed = await OmsWebfeed.create({
            customer: form_name,
            country_id: countryId,
            mobileno: contact_no,
            product_code: product,
            order_from: orderFrom,
            emirate,
            area,
            address: delivery_address,
            order_date: currentDate,
            price,
            quantity,
            status: "Pending",
        });

        if (webFeed) {
            return {
                statusCode: 200,
                message:
                    "Your Order Has Been Placed Successfully. Our Customer Service Agent Will Contact You Shortly",
                status: true,
                data: {},
            };
        } else {
            return {
                statusCode: 200,
                message: "Something went wrong! While processing your order",
                status: false,
                data: {},
            };
        }
    } catch (err) {
        console.error("addFeedService error:", err);
        throw err;
    }
};

export const checkOrderService = async (req) => {
    try {
        const { invoice } = req.body;
        if (!invoice) {
            return {
                statusCode: 400,
                message: "Invoice/order reference is required",
                status: false,
                data: [],
            };
        }
        // Search by invoice_no or order_ref_code
        const order = await OmsOrders.findOne({
            where: {
                [Op.or]: [{ invoice_no: invoice }, { order_ref_code: invoice }],
            },
            raw: true,
        });
        if (order) {
            return {
                statusCode: 200,
                message: "Order found",
                status: true,
                data: [
                    {
                        orderid: order.orderid,
                        invoice_no: order.invoice_no,
                        order_ref_code: order.order_ref_code,
                    },
                ],
            };
        } else {
            return {
                statusCode: 404,
                message: "Please Enter valid Order Id",
                status: false,
                data: [],
            };
        }
    } catch (err) {
        throw err;
    }
};

export const trackYourOrderService = async (req) => {
    try {
        const CDN_IMAGE_BASE_URL = process.env.CDN_IMAGE_BASE_URL;
        const userId = req.user?.user_id;
        const countryId = req.country_id;
        const { orderid, contact } = req.query;

        // Get orders from oms_orders table
        const order = await OmsOrders.findOne({
            where: {
                customerid: userId,
                order_ref_code: orderid,
                // customer_contact: contact,
                country_id: countryId,
            },
            raw: true,
        });

        if (order.length === 0) {
            return {
                statusCode: 200,
                message: "No orders found",
                status: true,
                data: {},
            };
        }

        // Get order IDs for related queries
        const orderId = order.orderid;

        // Get order details (products)
        const orderDetails = await OmsOrderDetail.findAll({
            where: {
                orderid: orderId,
            },
            raw: true,
        });

        // Get product details separately
        const productIds = orderDetails
            .map((detail) => detail.productid)
            .filter(Boolean);
        let products = [];
        if (productIds.length > 0) {
            try {
                products = await CatalogProduct.findAll({
                    where: {
                        productid: productIds,
                    },
                    raw: true,
                });
            } catch (error) {
                // If catalog_product table doesn't exist, continue with empty array
                products = [];
            }
        }

        // Get order history for status tracking
        const orderHistory = await OmsOrderHistory.findAll({
            where: {
                orderid: orderId,
            },
            order: [["statusdate", "ASC"]],
            raw: true,
        });

        // Process and format the results
        const invoiceBaseUrl =
            process.env.INVOICE_BASE_URL || "https://www.ourshopee.com";

        // Parse customer_details JSON
        let customerDetails = {};
        if (typeof order.customer_details === "string") {
            try {
                customerDetails = JSON.parse(order.customer_details);
            } catch (e) {
                customerDetails = {};
            }
        } else if (order.customer_details) {
            customerDetails = order.customer_details;
        }

        // Parse shipping address JSON
        let shippingAddress = {};
        if (typeof order.shippingaddress === "string") {
            try {
                shippingAddress = JSON.parse(order.shippingaddress);
            } catch (e) {
                shippingAddress = {};
            }
        } else if (order.shippingaddress) {
            shippingAddress = order.shippingaddress;
        }

        // Date calculations

        const ordered_date = formatOrderDate(order.order_date);
        const packed_date = formatOrderDate(order.order_date, 2);
        const dispatched_date = formatOrderDate(order.order_date, 3);
        const delivery_date = formatOrderDate(order.order_date, 5);

        // Get order history for this specific order
        const thisOrderHistory = orderHistory.filter(
            (history) => history.orderid === order.orderid
        );

        // Determine order status flags based on order history
        let process_date_over = false;
        let pack_date_over = false;
        let dispatch_date_over = false;
        let delivery_date_over = false;
        let cancelledFlag = false;
        let cancelledDate = null;
        let invoiceLink = null;

        // Check if order is cancelled
        if (order.order_statusid === 4 || order.order_status === "Cancelled") {
            cancelledFlag = true;
            const cancelHistory = thisOrderHistory.find(
                (h) => h.order_statusid === 4
            );
            if (cancelHistory) {
                cancelledDate = moment(cancelHistory.statusdate).format(
                    "ddd, MMM Do, h:mm A"
                );
            }
        }

        // Check order progress based on history
        if (thisOrderHistory.length > 0) {
            const processedHistory = thisOrderHistory.find(
                (h) => h.order_statusid === 2
            );
            const packedHistory = thisOrderHistory.find(
                (h) => h.order_statusid === 3
            );
            const dispatchedHistory = thisOrderHistory.find(
                (h) => h.order_statusid === 5
            );
            const deliveredHistory = thisOrderHistory.find(
                (h) => h.order_statusid === 6
            );

            if (processedHistory) process_date_over = true;
            if (packedHistory) pack_date_over = true;
            if (dispatchedHistory) dispatch_date_over = true;
            if (deliveredHistory) {
                delivery_date_over = true;
                invoiceLink = `${invoiceBaseUrl}order_id=${order.orderid}&refid=${order.order_ref_code}`;
            }
        }

        /* const senderDetails = await AauthUsers.findOne({
            where: {
                id: order.driver_id,
            },
            raw: true,
        });
        const senderFirstName = senderDetails.firstname
            ? senderDetails.firstname
            : "";
        const senderLastName = senderDetails.lastname
            ? senderDetails.lastname
            : "";
        const deliveryName = `${senderFirstName} ${senderLastName}`;
        const deliveryContact = senderDetails.mobile
            ? senderDetails.mobile
           : "";

            let acceptDate = "";
            orderHistory.forEach((history) => {
                if(history.order_statusid == 1){

                }
                if(history.order_statusid == 2){
                    acceptDate = moment(history.statusdate).format(
                        "ddd, MMM Do, h:mm A"
                    );
                }


            }) */

        // Get order items
        const items = orderDetails
            // .filter((detail) => detail.orderid === order.orderid)
            .map((item) => {
                // Find corresponding product
                const product = products.find(
                    (p) => p.productid === item.productid
                );

                let productName = `Product ${item.productid}`;
                let productImage = "default-product.jpg";

                if (product) {
                    productName = product.title || productName;

                    if (product.product_images) {
                        try {
                            const parsedImages =
                                typeof product.product_images === "string"
                                    ? JSON.parse(product.product_images)
                                    : product.product_images;

                            // Extract image URL from the nested structure
                            let imageUrl = null;

                            if (
                                parsedImages.image &&
                                Array.isArray(parsedImages.image) &&
                                parsedImages.image.length > 0
                            ) {
                                imageUrl =
                                    parsedImages.image[0].file_url ||
                                    parsedImages.image[0].original;
                            } else if (
                                parsedImages.web_images &&
                                Array.isArray(parsedImages.web_images) &&
                                parsedImages.web_images.length > 0
                            ) {
                                imageUrl =
                                    parsedImages.web_images[0].file_url ||
                                    parsedImages.web_images[0].original;
                            } else if (
                                Array.isArray(parsedImages) &&
                                parsedImages.length > 0
                            ) {
                                imageUrl = parsedImages[0];
                            }

                            if (imageUrl) {
                                const cleanImageUrl = imageUrl.startsWith("/")
                                    ? imageUrl.substring(1)
                                    : imageUrl;
                                productImage = `${CDN_IMAGE_BASE_URL}/${cleanImageUrl}`;
                            }
                        } catch (e) {
                            // Continue with default image if parsing fails
                        }
                    }
                }

                const itemQuantity = item.quantity || 1;

                return {
                    name: productName,
                    image: productImage,
                    product_id: item.productid,
                    // amount: item.amount || 0,
                    price: item.selling_price || 0,
                    quantity: itemQuantity,
                };
            });

        const formattedOrders = {
            referenceNo: order.order_ref_code,
            orderId: order.orderid,
            totalAmount: parseInt(order.total_amount) || 0,
            // totalAmount: order.total_amount || 0,
            placedOn: moment(order.order_date).format("YYYY-MM-DD h:mm A"),
            items: items,
            cancelled: cancelledFlag,
            ...(cancelledFlag && {
                cancelledText:
                    order.payment_status === "Failed"
                        ? "Due to online transaction error, your item has been cancelled"
                        : "As per your request, your item has been cancelled",
            }),
            ...(cancelledFlag && cancelledDate && { cancelledDate }),
            processDateOver: process_date_over,
            packDateOver: pack_date_over,
            dispatchDateOver: dispatch_date_over,
            deliverydateOver: delivery_date_over,
            invoice: invoiceLink ? invoiceLink : "",
            orderDetail: {
                order_id: order.orderid,
                order_refid: order.order_ref_code,
                user_name: customerDetails?.name || "Customer",
                mobile:
                    customerDetails?.mobile || customerDetails?.phone || "N/A",
                paymode: order.payment_method || "N/A",
                total_amount: parseInt(order.total_amount) || 0,
                shipping_charge: order.shipping_charges || 0,
                processing_fee: addZeroes(order.processing_fee) || "0.00",
                discount: addZeroes(order.discount_amount) || "0.00",
                order_date: order.order_date,
                order_status: order.payment_status || "N/A",
                shipping_address:
                    shippingAddress?.address ||
                    (typeof shippingAddress === "object"
                        ? JSON.stringify(shippingAddress)
                        : shippingAddress || "N/A"),
                vat: addZeroes(order.tax_amount) || "0.00",
                orderedDate: ordered_date,
                processedDate: ordered_date,
                packedDate: packed_date,
                dispatchedDate: dispatched_date,
                deliveryDate: delivery_date,
            },
            // sender_details: {
            //     sender_id: order.driver_id,
            //     shipping_date: "",
            //     accept_date: acceptDate,
            //     received: "",
            //     delivery_name: deliveryName ? deliveryName : "",
            //     delivery_contact: deliveryContact,
            //     sender_status: "",
            //     customer_delivery_date: "",
            //     return_date: "",
            // },
        };

        return {
            statusCode: 200,
            message: "Orders fetched successfully",
            status: true,
            data: [formattedOrders],
        };
    } catch (err) {
        throw err;
    }
};
