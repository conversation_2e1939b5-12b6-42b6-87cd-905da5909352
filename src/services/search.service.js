import { searchProductsELK, searchResultItemsELK } from "../elk/search.elk.js";
import CatalogProduct from "../models/catalog_product.model.js";
import { Op } from "sequelize";
import { transformProductItem } from "./common.service.js";
import { CURRENCY } from "../region/config.js";
import CatalogProductInventory from "../models/catalog_product_inventory.model.js";
import CatalogBrand from "../models/catalog_brand.model.js";
import CatalogCategory from "../models/catalog_category.model.js";

export const searchProductsService = async (req) => {
    try {
        const searchProducts = await searchProductsELK(req);

        return {
            statusCode: 200,
            message: "Products data fetched successfully!",
            data: searchProducts,
        };
    } catch (err) {
        throw err;
    }
};

export const searchResultItemsService = async (req) => {
    try {
        const searchProducts = await searchResultItemsELK(req);

        return {
            statusCode: 200,
            message: "Products data fetched successfully!",
            data: searchProducts,
        };
    } catch (err) {
        throw err;
    }
};

export const appSearchproductsService = async (req) => {
    try {
        const keyword = (req.query.str || "").trim();
        if (!keyword) {
            return {
                tag: "SearchDropdown",
                error: true,
                msg: "Keyword is required",
            };
        }

        const words = keyword.split(/\s+/);

        // Build dynamic WHERE conditions
        const whereClause = {
            [Op.and]: words.map((word) => ({
                [Op.or]: [
                    { title: { [Op.like]: `%${word}%` } },
                    { sku: { [Op.like]: `%${word}%` } },
                ],
            })),
        };

        const { country_id } = req;

        const products = await CatalogProduct.findAll({
            where: whereClause,
            attributes: [
                "productid",
                "brandid",
                "categoryid",
                "title",
                "sku",
                "product_images",
                "slug",
            ],
            include: [
                {
                    model: CatalogProductInventory,
                    as: "inventory",
                    attributes: [
                        "inventory", // stock qty
                        "mrp",
                        "selling_price",
                        "promotion_price",
                        "stock_status",
                        "offer_from",
                        "offer_to",
                    ],
                    where: {
                        country_id,
                        vendor_id: 0,
                        stock_status: 1,
                        rstatus: 1,
                        selling_price: { [Op.gt]: 0 },
                    },
                    required: true, // makes it a Inner JOIN
                },
                {
                    model: CatalogBrand,
                    as: "brands",
                    attributes: ["brandid", "brand_name", "arabic_name"],
                },
                {
                    model: CatalogCategory,
                    as: "subcategory",
                    attributes: ["category_name", "arabic_name"],
                },
            ],
            order: [["updated_date", "DESC"]],
            limit: 10,
        });

        const final_array = { products: [] };

        for (const product of products) {
            // const brand = await OurshopeeBrand.findOne({
            //     where: { brandid: product.brandid },
            //     attributes: ["brand_name", "arabic_name"],
            // });

            // const subcat = await CatalogCategory.findOne({
            //     where: { categorypid: product.subcategory_id },
            //     attributes: ["name", "name_arabic"],
            // });
            const brand = product.brands || {};
            const subcategory = product.subcategory || {};

            const images = JSON.parse(product.product_images || "{}");

            const productImage = images.web_images?.[0]?.file_url || null;

            final_array.products.push({
                title: product.title,
                image: `${process.env.CDN_IMAGE_BASE_URL}${productImage}`,
                small_title: "",
                type: "detail",
                url: `${product.slug}/${product.sku}`,
                brand_name: brand?.brand_name || "",
                brand_name_arabic: brand?.arabic_name || "",
                sucat_name: subcategory?.category_name || "",
                sucat_name_arabic: subcategory?.arabic_name || "",
            });
        }

        return {
            statusCode: 200,
            message: "Products data fetched successfully!",
            data: final_array,
        };
    } catch (err) {
        throw err;
    }
};

export const appSearchResultItemsService = async (req) => {
    try {
        const currency = CURRENCY[req.country_id].currency;
        const keyword = (req.query.string || "").trim();
        const page = parseInt(req.query.page) || 1;
        const limit = 10;
        const offset = (page - 1) * limit;

        if (!keyword) {
            return {
                statusCode: 200,
                message: "Products data fetched successfully!",
                data: {
                    filters: {},
                    display_items: {
                        top_brands: [],
                        products: [],
                    },
                },
            };
        }

        const words = keyword.split(/\s+/);

        // const keywordConditions = words.map((word) => ({
        //     [Op.or]: [
        //         { title: { [Op.like]: `%${word}%` } },
        //         { sku: { [Op.like]: `%${word}%` } },
        //     ],
        // }));

        // const whereClause = {
        //     [Op.and]: [...keywordConditions],
        // };

        const whereClause = {
            [Op.and]: words.map((word) => ({
                [Op.or]: [
                    { title: { [Op.like]: `%${word}%` } },
                    { sku: { [Op.like]: `%${word}%` } },
                ],
            })),
        };

        // Step 1: Get total count
        const { country_id } = req;

        // Step 2: Fetch paginated product results
        const products = await CatalogProduct.findAll({
            where: {
                ...whereClause,
                vendor_id: 0,
            },

            attributes: [
                "productid",
                "brandid",
                "categoryid",
                "title",
                "sku",
                "product_images",
                "slug",
            ],
            include: [
                {
                    model: CatalogProductInventory,
                    as: "inventory",
                    attributes: [
                        "inventory", // stock qty
                        "mrp",
                        "selling_price",
                        "promotion_price",
                        "stock_status",
                        "offer_from",
                        "offer_to",
                    ],
                    where: {
                        vendor_id: 0,
                        country_id,
                        stock_status: 1,
                        rstatus: 1,
                        selling_price: { [Op.gt]: 0 },
                    },
                    required: true, // makes it a Inner JOIN
                },
            ],
            order: [["updated_date", "DESC"]],
            offset,
            limit,
        });

        if (products && products.length === 0) {
            return {
                statusCode: 200,
                message: "Products data fetched successfully!",
                data: {
                    filters: {},
                    display_items: {
                        top_brands: [],
                        products: [],
                    },
                },
            };
        }

        // Step 3: Format products
        const formattedProducts = products.map((item) => {
            const inventory = item.inventory || {};
            const itemData = { ...(item.toJSON?.() || item) };

            const primage = itemData.product_images; // This should be a JSON string
            const parsed = JSON.parse(primage);
            let image;
            if (
                parsed &&
                Array.isArray(parsed.web_images) &&
                parsed.web_images.length > 0 &&
                parsed.web_images[0].original
            ) {
                image = parsed.web_images[0].original;
            }
            return {
                id: itemData.productid,
                brand_id: itemData.brandid,
                subcategory_id: itemData.categoryid,
                ...transformProductItem(
                    {
                        id: itemData.productid,
                        name: itemData.title,
                        image,
                        url: itemData.slug,
                        sku: itemData.sku,
                        inventory: inventory.inventory,
                        price: inventory.mrp,
                        selling_price: inventory.selling_price,
                        special_price: inventory.selling_price,
                        promotion_price: inventory.promotion_price,
                        from_date: inventory.offer_from,
                        to_date: inventory.offer_to,
                    },
                    currency
                ),
            };
        });

        // Step 4: Return result

        return {
            statusCode: 200,
            message: "Products data fetched successfully!",
            data: {
                filters: {},
                display_items: {
                    top_brands: [],
                    products: formattedProducts,
                },
            },
        };
    } catch (err) {
        throw err;
    }
};
