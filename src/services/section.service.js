import CmsPost from "../models/cms_post.model.js";
import CmsSection from "../models/cms_section.model.js";
import CmsSectionPageDetails from "../models/cms_section_page_details.model.js";

import { getProductsBySectionElk } from "../elk/common.elk.js";
import {
    getSpecialDealsProductsELK,
    getComboDealsProductsELK,
    eidResultELK,
    getInfinteScrollItemsELK,
} from "../elk/section.elk.js";
import { COUNTRY_NAMES, CURRENCY, sectionConfig } from "../region/config.js";
import {
    getBlogCustomData,
    getCmsCustomData,
    getSliderCustomData,
    transformProductItem,
} from "./common.service.js";
import CatalogCategory from "../models/catalog_category.model.js";
import CmsSectionPage from "../models/cms_section_page.model.js";
import CatalogBrand from "../models/catalog_brand.model.js";
import CmsBanners from "../models/cms_banners.model.js";

export const getBlogsService = async () => {
    try {
        // Fetch categories
        const categories = await CatalogCategory.findAll({
            where: { category_type: 2 },
            order: [["categoryid", "ASC"]],
            attributes: ["categoryid", "category_name", "slug"],
            raw: true,
        });

        // Fetch recent blogs
        const recentBlogs = await CmsPost.findAll({
            where: { post_status: 1 },
            order: [["published_on", "DESC"]],
            limit: 15,
            attributes: [
                "postid",
                "title",
                "published_on",
                "post_images",
                "short_description",
                "slug",
            ],
            raw: true,
        });

        return {
            Categories: categories.map((ele) => ({
                id: ele.categoryid,
                name: ele.category_name,
                url: ele.slug,
            })),
            Recents: recentBlogs.map(formatBlog),
        };
    } catch (err) {
        throw err;
    }
};

export const getBlogByCategoryIdService = async (req) => {
    try {
        let categoryBlogs = [];
        let recentBlogs = [];

        // 🧠 Sanitize inputs
        const { id, blogId } = req.query;
        const attributes = [
            "postid",
            "title",
            "published_on",
            "post_images",
            "short_description",
            "slug",
        ];
        if (id) {
            // Fetch blogs by category ID
            categoryBlogs = await CmsPost.findAll({
                attributes,
                where: {
                    post_status: 1,
                    categoryid: id,
                },
                order: [["published_on", "DESC"]],
                raw: true,
            });
        } else if (blogId) {
            // Fetch specific blog by ID
            const blog = await CmsPost.findOne({
                attributes,
                where: { postid: blogId },
                raw: true,
            });

            if (blog) {
                categoryBlogs = [blog];
            }
        }

        // Fetch recent blogs
        recentBlogs = await CmsPost.findAll({
            where: { post_status: 1 },
            order: [["published_on", "DESC"]],
            limit: 5,
            raw: true,
        });

        return {
            categoryList: categoryBlogs.map(formatBlog),
            Recents: recentBlogs.map(formatBlog),
        };
    } catch (err) {
        throw err;
    }
};

const formatBlog = (ele) => {
    const data = getBlogCustomData(ele.post_images, ele.type);

    return {
        id: ele.postid,
        title: ele.title,
        display_date: ele.published_on,
        image: data.image ? data.image : "",
        description: ele.short_description,
        url: ele.slug,
    };
};

const clearanceSaleCommon = async (req, inputData) => {
    try {
        // Validate inputs
        if (!req.country_id || !CURRENCY[req.country_id]) {
            throw new Error(`Invalid country_id: ${req.country_id}`);
        }

        let topItemsList = await getProductsBySectionElk(req, inputData);

        const uniqueItems = new Map();

        topItemsList.forEach((item) => {
            if (!uniqueItems.has(item.id)) {
                uniqueItems.set(item.id, item);
            }
        });

        const currency = CURRENCY[req.country_id].currency;

        topItemsList = Array.from(uniqueItems.values()).map((item) =>
            transformProductItem(item, currency)
        );

        return topItemsList;
    } catch (err) {
        // Return empty array instead of throwing to prevent API crash
        return [];
    }
};

export const clearanceSaleService = async (req) => {
    try {
        // Validate required parameters
        if (!req.country_id) {
            throw new Error("Country ID is missing from request");
        }

        if (!sectionConfig[req.country_id]) {
            throw new Error(
                `No section config found for country_id: ${req.country_id}`
            );
        }

        const { clearance_sale_sec_id } = sectionConfig[req.country_id];

        if (!clearance_sale_sec_id) {
            throw new Error(
                `No clearance_sale_sec_id found for country_id: ${req.country_id}`
            );
        }

        // Ensure req.query exists
        if (!req.query) {
            req.query = {};
        }

        req.query.sec_id = clearance_sale_sec_id;
        const { limit = 4 } = req.query;
        const meta_tags = await sectionMetatags(req);
        let bannerImages = [];
        let page = req.query.page;
        let top_items = [];
        let items = [];

        let inputData = {
            section_id: clearance_sale_sec_id,
            offset: 0,
            limit: Number(limit),
            front_view: 1,
            condition_from_date: false,
        };

        if (page === "1") {
            bannerImages = await sectionImages(req);
            top_items = await clearanceSaleCommon(req, inputData);
        }

        const limit2 = 24;
        const offset =
            typeof page !== "undefined" ? (parseInt(page) - 1) * limit2 : 0;
        inputData.front_view = 0;
        inputData.offset = offset;
        inputData.limit = Number(limit2);

        items = await clearanceSaleCommon(req, inputData);

        return { top_items, items, bannerImages, meta_tags };
    } catch (err) {
        throw err;
    }
};

const sectionMetatags = async (req) => {
    try {
        let { section_id, sec_id } = req.query;
        if (sec_id > 0) {
            section_id = sec_id;
        }

        if (!section_id) {
            return [];
        }

        const { country_id } = req;

        if (!country_id) {
            return [];
        }

        const result = await CmsSection.findAll({
            attributes: ["seo_title", "seo_description", "seo_keywords"],
            where: { id: section_id, country_id },
            order: [["position", "ASC"]],
            raw: true,
        });

        return result || [];
    } catch (err) {
        // Return empty array instead of throwing to prevent API crash
        return [];
    }
};

const sectionImages = async (req) => {
    try {
        let { section_id, sec_id } = req.query;
        if (section_id > 0) {
            sec_id = section_id;
        }

        if (!sec_id) {
            return [];
        }

        const { country_id } = req;

        if (!country_id) {
            return [];
        }

        const result = await CmsBanners.findAll({
            where: {
                country_id: country_id,
                type_id: sec_id,
                rstatus: 1,
                type: 5,
            },
            order: [["position", "ASC"]],
            limit: 5,
            attributes: ["id", "type", "banner_images"],
            raw: true,
            // logging: console.log,
        });

        if (!result || result.length === 0) {
            return [];
        }

        let pathname = "#";
        const output = result.map((el) => {
            const d = getCmsCustomData(el.banner_images);

            try {
                if (d.url && d.url !== "" && d.url !== "#") {
                    const domain = new URL(d.url);
                    pathname = domain.pathname;
                } else {
                    pathname = d.url || "#";
                }
            } catch (urlError) {
                pathname = d.url || "#";
            }

            return {
                id: el.id,
                image_url: d.image ? d.image : "",
                mobile_image_url: d.mobile_image ? d.mobile_image : "",
                url: pathname,
            };
        });
        return output;
    } catch (err) {
        // Return empty array instead of throwing to prevent API crash
        return [];
    }
};

export const dealOfTheDayService = async (req) => {
    try {
        const {
            deal_of_the_day_sec_id,
            get_special_deal_sec_id,
            get_special_deal_check_in_sec_id,
        } = sectionConfig[req.country_id];

        const countryId = req.country_id;
        const currency = CURRENCY[req.country_id].currency;
        req.query.sec_id = deal_of_the_day_sec_id;
        //let page = req.query.page == "1" ? 0 + 24 : (req.query.page - 1) * 20 + 24;
        const rawPage = parseInt(req.query.page);
        const page =
            isNaN(rawPage) || rawPage <= 1 ? 0 : (rawPage - 1) * 20 + 24;
        const section_metatags = await sectionMetatags(req);
        const bannerImages = await sectionImages(req);
        const hotDeals = await getSpecialDealsProductsELK(
            get_special_deal_sec_id,
            24,
            0,
            req,
            get_special_deal_check_in_sec_id
        );
        const trendingProducts = await getSpecialDealsProductsELK(
            get_special_deal_sec_id,
            20,
            page,
            req,
            get_special_deal_check_in_sec_id
        );

        const return_output = {
            banner_image: bannerImages,
            meta_tags: section_metatags,
            hot_deals: hotDeals.slice(0, 4),
            items: hotDeals.slice(4, 24),
            trending_products: trendingProducts,
        };
        return return_output;
    } catch (err) {
        throw err;
    }
};

export const topSellingProductsService = async (req) => {
    try {
        const currency = CURRENCY[req.country_id].currency;
        const CDN_IMAGE_BASE_URL = process.env.CDN_IMAGE_BASE_URL;

        const { top_selling_products_sec_id } = sectionConfig[req.country_id];

        req.query.sec_id = top_selling_products_sec_id;
        const page = parseInt(req.query.page) || 1;
        const limit = 20;
        let offset = (page - 1) * limit;
        const sectionMetaTags = await sectionMetatags(req);

        const result = {
            banner_image: [
                {
                    id: 1,
                    image_url: `${CDN_IMAGE_BASE_URL}/ourshopee-img/ourshopee_categorybanner/1895826381_Mobiles&Tablets.jpg`,
                    mobile_image_url: `${CDN_IMAGE_BASE_URL}/ourshopee-img/ourshopee_categorybanner/1895826381_Mobiles&Tablets.jpg`,
                    url: "",
                },
            ],
            items: [],
            top_Selling_products: [],
            meta_tags: sectionMetaTags,
        };

        // Fetch top 4 items for first page only

        if (page === 1) {
            const topItems = await getProductsBySectionElk(req, {
                section_id: top_selling_products_sec_id,
                offset: 0,
                limit: 4,
            });

            const uniqueTopItems = deduplicateById(topItems);
            result.items = uniqueTopItems.map((item) =>
                transformProductItem(item, currency)
            );
            offset += 4;
        }

        // Fetch main list (offset handles overlap if page === 1)
        const mainItems = await getProductsBySectionElk(req, {
            section_id: top_selling_products_sec_id,
            offset,
            limit,
            condition_from_date: false,
            condition_quantity: true,
        });

        const uniqueMainItems = deduplicateById(mainItems);
        result.top_Selling_products = uniqueMainItems.map((item) =>
            transformProductItem(item, currency)
        );

        return result;
    } catch (err) {
        throw err;
    }
};

const deduplicateById = (items) => {
    const seen = new Set();
    return items.filter((item) => {
        if (seen.has(item.id)) return false;
        seen.add(item.id);
        return true;
    });
};

const safeCreateURL = (url, base) => {
    try {
        if (!url || url.trim() === "") {
            return { pathname: "#", search: "" };
        }
        const urlObj = new URL(url, base);
        return urlObj;
    } catch (error) {
        return { pathname: url || "#", search: "" };
    }
};

export const saverZoneService = async (req) => {
    try {
        const CDN_IMAGE_BASE_URL = process.env.CDN_IMAGE_BASE_URL;
        const countryBaseEnv = COUNTRY_NAMES[req.country_id];
        const BASE_URL = process.env[`${countryBaseEnv}_BASE_URL`];

        // const BASE_URL = process.env.BASE_URL;
        const sec_id = req.query.section_id;
        const eid = req.query.eid_sale;
        req.query.sec_id = sec_id;
        // Validate required environment variables
        if (!BASE_URL) {
            throw new Error("BASE_URL environment variable is required");
        }
        if (!CURRENCY[req.country_id]) {
            throw new Error(`Invalid country_id: ${req.country_id}`);
        }
        const currency = CURRENCY[req.country_id].currency;
        // Helper function to safely create URLs

        const section_page = await CmsSectionPage.findAll({
            attributes: [
                "id",
                ["title_en", "heading"],
                "title_ar",
                "title_display",
                "position",
                "status",
                "html_type",
                "main_css",
                "list_css",
            ],
            where: { section_id: sec_id, status: 1 },
            order: [["position", "ASC"]],
            include: [
                {
                    model: CmsSectionPageDetails,
                    as: "details",
                    attributes: [
                        "id",
                        "page_id",
                        "subcategory_id",
                        "sub_sub_cat_id",
                        "brand_id",
                        ["en_redirection", "redirection_url"],
                        "tag_id",
                        "pagedetail_images",
                    ],
                    include: [
                        {
                            model: CatalogCategory,
                            as: "subcategory",
                            attributes: [
                                "categoryid",
                                ["category_name", "subcatname"],
                                ["slug", "subcaturl"],
                            ],
                        },
                        {
                            model: CatalogCategory,
                            as: "sub_subcategory",
                            attributes: [
                                "categoryid",
                                ["category_name", "subsubname"],
                                ["slug", "subsuburl"],
                            ],
                        },
                        {
                            model: CatalogBrand,
                            as: "brand",
                            attributes: [
                                ["brand_name", "brandname"],
                                ["slug", "brandurl"],
                            ],
                        },
                    ],
                },
            ],
        });

        // console.log('section_page', JSON.stringify(section_page));

        const section_metatags = await sectionMetatags(req);
        const banner_images = await sectionImages(req);
        const eid_results = eid ? await eidResultELK(req) : [];
        const uniqueBlocks = section_page.map((ele) => ele.toJSON());
        const outputBlocks = await Promise.all(
            uniqueBlocks.map(async (ele, blockIndex) => {
                try {
                    // console.log(uniqueBlocks)
                    // console.log('ele.title_display', ele.title_display);
                    // console.log('ele.heading', ele.heading);

                    const html_type = ele.html_type;

                    const main_title =
                        ele.title_display === "1" || ele.title_display === 1
                            ? ele.heading
                            : "";
                    // console.log('html_type', html_type)
                    switch (html_type) {
                        case 1:
                            return {
                                heading: main_title,
                                title_display: ele.title_display,
                                type: html_type,
                                list_css: ele.list_css,
                                main_css: ele.main_css,
                                items: (ele.details || []).map(
                                    (detail, index) => {
                                        const values = section_dynamic_cond(
                                            detail,
                                            req
                                        );
                                        const domain = safeCreateURL(
                                            values.redirection || "",
                                            BASE_URL
                                        );

                                        // console.log('detail.pagedetail_images', detail.pagedetail_images);
                                        const data = getCmsCustomData(
                                            detail.pagedetail_images
                                        );

                                        return {
                                            id: index + 1,
                                            url: domain.pathname,
                                            sub_category_image: data.image
                                                ? `${data.image}`
                                                : "",
                                            sub_category_name:
                                                detail.subcategory
                                                    ?.subcatname || "",
                                        };
                                    }
                                ),
                            };

                        case 2:
                            return {
                                heading: main_title,
                                title_display: ele.title_display,
                                type: "brands",
                                list_css: ele.list_css,
                                main_css: ele.main_css,
                                items: (ele.details || []).map(
                                    (detail, index) => {
                                        const values = section_dynamic_cond(
                                            detail,
                                            req
                                        );
                                        const data = getCmsCustomData(
                                            detail.pagedetail_images
                                        );

                                        return {
                                            url: values.redirection,
                                            id: detail.brand_id,
                                            desktopImage: data.image
                                                ? `${data.image}`
                                                : "",
                                            mobileImage: data.mobile_image
                                                ? `${data.mobile_image}`
                                                : "",
                                        };
                                    }
                                ),
                            };

                        case 3:
                            const values3 = section_dynamic_cond(ele, req);
                            const domain3 = safeCreateURL(
                                values3.redirection || "",
                                BASE_URL
                            );

                            let pathname3 = domain3.pathname;
                            try {
                                if (
                                    domain3.pathname &&
                                    domain3.pathname.includes(".php") &&
                                    domain3.search
                                ) {
                                    const searchParams = new URLSearchParams(
                                        domain3.search
                                    );
                                    const categoryParam = Array.from(
                                        searchParams.entries()
                                    )[0];
                                    if (categoryParam && categoryParam[1]) {
                                        pathname3 = `/products-category/${categoryParam[1].split("&")[0]}`;
                                    }
                                }
                            } catch (error) {
                                pathname3 = domain3.pathname;
                            }

                            // For Case 3, images are in the first detail item, not in ele directly
                            const firstDetail = ele.details?.[0];
                            const data = getCmsCustomData(
                                firstDetail.pagedetail_images
                            );
                            return {
                                heading: main_title,
                                title_display: ele.title_display,
                                type: "single_image",
                                list_css: ele.list_css,
                                main_css: ele.main_css,
                                images: {
                                    url: pathname3,
                                    id: ele.id,
                                    desktopImage: data?.image
                                        ? `${data.image}`
                                        : "",
                                    mobileImage: data?.mobile_image
                                        ? `${data.mobile_image}`
                                        : "",
                                },
                            };

                        case 4:
                            return {
                                heading: main_title,
                                title_display: ele.title_display,
                                type: "multiple_images",
                                list_css: ele.list_css,
                                main_css: ele.main_css,
                                images: (ele.details || []).map(
                                    (detail, index) => {
                                        const values = section_dynamic_cond(
                                            detail,
                                            req
                                        );
                                        const domain = safeCreateURL(
                                            values.redirection || "",
                                            BASE_URL
                                        );
                                        let pathname = domain.pathname;
                                        try {
                                            if (
                                                domain.pathname &&
                                                domain.pathname.includes(
                                                    ".php"
                                                ) &&
                                                domain.search
                                            ) {
                                                const searchParams =
                                                    new URLSearchParams(
                                                        domain.search
                                                    );
                                                const categoryParam =
                                                    Array.from(
                                                        searchParams.entries()
                                                    )[0];
                                                if (
                                                    categoryParam &&
                                                    categoryParam[1]
                                                ) {
                                                    pathname = `/products-category/${categoryParam[1].split("&")[0]}`;
                                                }
                                            }
                                        } catch (error) {
                                            pathname = domain.pathname;
                                        }
                                        const data = getCmsCustomData(
                                            detail.pagedetail_images
                                        );

                                        return {
                                            url: pathname,
                                            list_css: ele.list_css,
                                            id: detail.id,
                                            desktopImage: data?.image
                                                ? `${data.image}`
                                                : "",
                                            mobileImage: data?.mobile_image
                                                ? `${data.mobile_image}`
                                                : "",
                                        };
                                    }
                                ),
                            };

                        case 5:
                            const values = section_dynamic_cond(
                                ele.details[0],
                                req
                            );

                            const domain = safeCreateURL(
                                values.redirection || "",
                                BASE_URL
                            );

                            let subCategoryId = 0;
                            let subsubCategoryId = 0;
                            let brandId = 0;
                            if (ele.details[0].subcategory_id) {
                                subCategoryId = ele.details[0].subcategory_id;

                                req.subCategoryId = subCategoryId;
                            }
                            if (ele.details[0].sub_sub_cat_id) {
                                subsubCategoryId =
                                    ele.details[0].sub_sub_cat_id;
                                req.subsubCategoryId = subsubCategoryId;
                            }
                            // console.log('ele.details[0].brand_id', )
                            if (ele.details[0].brand_id) {
                                brandId = ele.details[0].brand_id;
                                req.brandId = brandId;
                            }

                            const ui5 = await getSpecialDealsProductsELK(
                                0,
                                20,
                                0,
                                req,
                                [2],
                                subCategoryId,
                                subsubCategoryId,
                                brandId
                            );

                            // console.log("UI5 Data:", ui5);
                            return {
                                heading: main_title,
                                title_display: ele.title_display,
                                type: "category_items",
                                list_css: ele.list_css,
                                main_css: ele.main_css,
                                // items: ui5.map((item) k=>

                                //     transformProductItem(item, currency)
                                // ),
                                items: [
                                    {
                                        subcategory_name: main_title,
                                        url: domain.pathname,
                                        items: ui5,
                                    },
                                ],
                            };

                        case 8:
                            const ui8 = await getSpecialDealsProductsELK(
                                165,
                                12,
                                0,
                                req
                            );
                            return {
                                heading: main_title,
                                title_display: ele.title_display,
                                type: "items",
                                list_css: ele.list_css,
                                main_css: ele.main_css,
                                // items: ui11.map((item) =>
                                //     transformProductItem(item, currency)
                                // ),
                                items: ui8,
                            };

                        case 9:
                            const ui9 = await getComboDealsProductsELK(
                                req,
                                10,
                                0
                            );
                            return {
                                heading: main_title,
                                title_display: ele.title_display,
                                type: html_type,
                                list_css: ele.list_css,
                                main_css: ele.main_css,
                                // items: ui9.map((item) =>
                                //     transformProductItem(item, currency)
                                // ),
                                items: ui9,
                            };

                        case 11:
                            if (ele.tag_id) {
                                const row_sec_id = ele.tag_id.split("=")[1];
                                const ui11 = await getSpecialDealsProductsELK(
                                    row_sec_id,
                                    12,
                                    0,
                                    req
                                );
                                return {
                                    heading: main_title,
                                    title_display: ele.title_display,
                                    type: "items",
                                    list_css: ele.list_css,
                                    main_css: ele.main_css,
                                    // items: ui11.map((item) =>
                                    //     transformProductItem(item, currency)
                                    // ),
                                    items: ui11,
                                };
                            }
                            return null;

                        default:
                            return null;
                    }
                } catch (blockError) {
                    return null;
                }
            })
        );

        // console.log('outputBlocks', JSON.stringify(outputBlocks))
        const result = {
            eid_results,
            slider_images: banner_images,
            meta_tags: section_metatags,
            other_section: outputBlocks.filter(Boolean),
        };
        return result;
    } catch (err) {
        throw err;
    }
};

const section_dynamic_cond = (sub, req) => {
    const countryBaseEnv = COUNTRY_NAMES[req.country_id];
    const BASE_URL = process.env[`${countryBaseEnv}_BASE_URL`];
    // console.log('sub', sub.sub_subcategory.subsuburl)
    let redirection = "";
    try {
        // Use explicit safe checks
        if (sub.redirection_url && sub.redirection_url.trim() !== "") {
            redirection = sub.redirection_url;
        } else if (
            sub.subcategory?.subcaturl &&
            sub.sub_subcategory?.subsuburl
        ) {
            // redirection = `${process.env.PRODUCT_SUB_CATEGORY_PATH}${sub.subcategory.subcaturl}/${sub.sub_subcategory.subsuburl}/`;
            redirection = `${BASE_URL}/products-subcategory/${sub.subcategory.subcaturl}/${sub.sub_subcategory.subsuburl}/`;
        } else if (sub.brand?.brandurl && sub.subcategory?.subcaturl) {
            // redirection = `${process.env.PRODUCT_BRAND_PATH}${sub.brand.brandurl}/${sub.subcategory.subcaturl}/`;
            redirection = `${BASE_URL}/brands/${sub.brand.brandurl}/${sub.subcategory.subcaturl}/`;
        } else if (sub.subcategory?.subcaturl) {
            // redirection = `${process.env.PRODUCT_CATEGORY_PATH}${sub.subcategory.subcaturl}/`;
            redirection = `${BASE_URL}/products-category/${sub.subcategory.subcaturl}/`;
        }

        // console.log('sub', JSON.stringify(sub), 'redirection', redirection);
        const elk_cond_Array = {};
        if (sub.subcategory_id)
            elk_cond_Array.subcategory_id = sub.subcategory_id;
        if (sub.sub_sub_cat_id)
            elk_cond_Array.sub_sub_category_id = sub.sub_sub_cat_id;
        if (sub.brand_id) elk_cond_Array.brand_id = sub.brand_id;

        const listingCond = Object.entries(elk_cond_Array)
            .map(([key, val]) => `and ${key}=${val}`)
            .join(" ");

        const result = {
            redirection,
            listingCond,
            elk_cond_Array,
        };

        return result;
    } catch (error) {
        return {
            redirection: "",
            listingCond: "",
            elk_cond_Array: {},
        };
    }
};

export const metaTagService = async (req) => {
    try {
        const result = await sectionMetatags(req);
        return result;
    } catch (err) {
        throw err;
    }
};

export const getInfinteScrollItemsService = async (req) => {
    try {
        const result = await getInfinteScrollItemsELK(req);
        return result;
    } catch (err) {
        throw err;
    }
};
