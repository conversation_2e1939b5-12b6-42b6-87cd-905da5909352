// filtered_items
import {
    filteredItemsElk,
    filterProductsElk,
    searchOrFilteredItems,
} from "../elk/subCategory.elk.js";
import { CURRENCY } from "../region/config.js";
import { categories_elk } from "./category.service.js";
import { getCategoryBannerService } from "./brand.service.js";
import { transformProductItem } from "./common.service.js";

export const filteredItemsService = async (req) => {
    try {
        const currency = CURRENCY[req.country_id].currency;
        const elk_result = await searchOrFilteredItems(req);

        // Check if elk_result has products array
        if (!elk_result || !Array.isArray(elk_result.products)) {
            // console.log('No products found in elk_result:', elk_result);
            return {
                products: [],
                total_count: 0,
                ...elk_result,
            };
        }

        // Transform products using transformProductItem
        const transformedProducts = elk_result.products.map((item) =>
            transformProductItem(item, currency)
        );

        // console.log(`Transformed ${transformedProducts.length} products for filtered items`);

        // Return the elk_result with transformed products
        return {
            ...elk_result,
            products: transformedProducts,
        };
    } catch (err) {
        // console.error('Error in filteredItemsService:', err);
        throw err;
    }
};

export const getAllSubCategoryItemsService = async (request) => {
    try {
        const { subcat_url } = request.query;
        const currency = CURRENCY[request.country_id].currency;

        const elk_result = await filterProductsElk(request);
        const filter_results = await categories_elk();
        const firstProduct = elk_result.products?.[0];

        const bannerResult =
            firstProduct?.category_id || 0
                ? await getCategoryBannerService(
                      request.country_id,
                      firstProduct.category_id
                  )
                : [];

        const top_brands = (elk_result.top_brands || [])
            .map((ele) => {
                const brandData = ele.docs?.hits?.hits?.[0]?._source;
                const brand_image = brandData?.brand_image;

                if (brand_image) {
                    const splitImage = brand_image.split("ourshopee_brands")[1];
                    const isValid =
                        splitImage &&
                        splitImage !== "/" &&
                        splitImage.split(".")?.[1];

                    if (isValid) {
                        return {
                            id: brandData.brand_id,
                            brand_name: brandData.brand_name,
                            url: brandData.brand_url,
                            image: `${process.env.CDN_IMAGE_BASE_URL}${brand_image}`,
                        };
                    }
                }

                return null;
            })
            .filter(Boolean);

        const colors = (elk_result.colors || [])
            .map((ele) => {
                const color = ele.docs?.hits?.hits?.[0]?._source;
                return {
                    id: color?.color_id,
                    name: color?.color_name,
                };
            })
            .filter((c) => c?.id && c.id !== 0);

        const brands = (elk_result.brands || []).map((ele) => {
            const b = ele.docs?.hits?.hits?.[0]?._source;
            return {
                id: b?.brand_id,
                name: b?.brand_name,
            };
        });

        const categories = searchPages(filter_results, subcat_url);

        const displayItems = (elk_result.products || []).map((item) =>
            transformProductItem(item, currency)
        );

        // console.log('elk_result.max_price', elk_result.max_price.value)
        return {
            filters: {
                categories,
                slider_range: [
                    {
                        title: "price",
                        min_value: 0,
                        max_value: elk_result?.max_price?.value || 0,
                    },
                ],
                checkbox: [
                    {
                        id: Math.floor(Math.random() * 10000),
                        title: "Colors",
                        list: colors,
                    },
                    {
                        id: Math.floor(Math.random() * 10000),
                        title: "Brands",
                        list: brands,
                    },
                ],
            },
            display_items: {
                top_brands,
                banners: bannerResult,
                products: displayItems,
            },
        };
    } catch (err) {
        throw err;
    }
};

function searchPages(dataArray, targetUrl) {
    const match = dataArray.find((item) => {
        if (item.url?.includes(targetUrl)) return true;
        if (Array.isArray(item.subcategory)) {
            return item.subcategory.some((sub) => sub.url?.includes(targetUrl));
        }
        return false;
    });

    // If no match found or no subcategories, return empty array
    if (!match || !Array.isArray(match.subcategory)) return [];

    // Map subcategories
    return match.subcategory.map((sub) => ({
        category_id: sub.category_id,
        sub_category_id: sub.sub_category_id,
        value: `${sub.sub_category_id}@subcategory`,
        label: sub.sub_category_name,
        url: sub.url,
        children: (sub.sub_subcategory || []).map((subSub) => ({
            sub_category_id: subSub.sub_category_id,
            url: subSub.url,
            value: `${subSub.sub_subcategory_id}_${subSub.sub_category_id}@subsubcategory`,
            sub_subcategory_id: subSub.sub_subcategory_id,
            label: subSub.sub_subcategory_name,
        })),
    }));
}
