import adminVendor from "../models/admin_vendor.model.js";
import { Op } from "sequelize";

/**
 * @description Get vendor details
 * @param {string} vendor_id
 * @returns {Promise<Object|null>}
 */
export const getVendorDetails = async (vendor_id) => {
    try {
        const vendor = await adminVendor.findOne({
            where: { vendor_id: vendor_id },
        });
        return vendor || null;
    } catch (error) {
        throw error;
    }
};

export const getAllVendorsDetailsById = async (venderId = []) => {
    try {
        const vendors = await adminVendor.findAll({
            attributes: ["vendor_id", "business_name", "emaiid"],
            where: { vendor_id: { [Op.in]: venderId } },
        });
        return vendors || [];
    } catch (error) {
        throw error;
    }
};
