openapi: 3.0.0
info:
  title: OurShopee
  description: >-
    ### **About OurShopee**


    **OurShopee.com** is a leading e-commerce platform in the Middle East,
    offering a wide range of products including electronics, fashion, home
    appliances, and more. With a focus on customer satisfaction, competitive
    pricing, and fast delivery, OurShopee serves millions of users across the
    UAE, Oman, Qatar, Bahrain, and other Gulf countries.


    Our mission is to make online shopping easy, reliable, and affordable, while
    continually improving the customer experience through innovation and
    dedicated service.
  version: 1.0.0
servers:
  - url: https://api.ourshopee.com
components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
    apikeyAuth:
      type: http
      scheme: apikey
security:
  - apikeyAuth: []
tags:
  - name: Pre Login
  - name: Cart
  - name: Category
  - name: Products
  - name: Home
  - name: Master
  - name: Post Login
  - name: Orders
  - name: Addresses
    description: Its all about user loggedin addresses
  - name: Complents
paths:
  /api/CheckEmail:
    post:
      tags:
        - Pre Login
      summary: CheckEmail
      description: |-
        Generated from cURL: curl 'https://api.ourshopee.com/api/CheckEmail' \
          -H 'sec-ch-ua-platform: "macOS"' \
          -H 'Referer: https://ourshopee.com/' \
          -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
          -H 'Accept: application/json, text/plain, */*' \
          -H 'sec-ch-ua: "Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"' \
          -H 'Content-Type: application/json' \
          -H 'sec-ch-ua-mobile: ?0' \
          --data-raw '{"email":"<EMAIL>"}'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              example:
                email: <EMAIL>
      responses:
        "200":
          description: Successful response
          content:
            application/json: {}
  /api/checkMobile:
    post:
      tags:
        - Pre Login
      summary: checkMobile
      description: |-
        Generated from cURL: curl 'https://api.ourshopee.com/api/checkMobile' \
          -H 'sec-ch-ua-platform: "macOS"' \
          -H 'Referer: https://ourshopee.com/' \
          -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
          -H 'Accept: application/json, text/plain, */*' \
          -H 'sec-ch-ua: "Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"' \
          -H 'Content-Type: application/json' \
          -H 'sec-ch-ua-mobile: ?0' \
          --data-raw '{"mobile":"508435333"}'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              example:
                mobile: "54756745"
      responses:
        "200":
          description: Successful response
          content:
            application/json: {}
  /api/verifyMobileOtp:
    post:
      tags:
        - Pre Login
      summary: verifyMobileOtp
      description: >-
        Generated from cURL: curl
        'https://apikwtdev.ourshopee.com/api/verifyMobileOtp' \
          -H 'accept: application/json, text/plain, */*' \
          -H 'accept-language: en-US,en;q=0.9' \
          -H 'content-type: application/json' \
          -H 'origin: https://kuwaitdev.ourshopee.com' \
          -H 'priority: u=1, i' \
          -H 'referer: https://kuwaitdev.ourshopee.com/' \
          -H 'sec-ch-ua: "Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"' \
          -H 'sec-ch-ua-mobile: ?0' \
          -H 'sec-ch-ua-platform: "macOS"' \
          -H 'sec-fetch-dest: empty' \
          -H 'sec-fetch-mode: cors' \
          -H 'sec-fetch-site: same-site' \
          -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
          --data-raw '{"mobile":"76958485","otp":"9357"}'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              example:
                mobile: "54756745"
                otp: "2163"
      responses:
        "200":
          description: Successful response
          content:
            application/json: {}
  /api/Signup:
    post:
      tags:
        - Pre Login
      summary: Signup
      description: |-
        Generated from cURL: curl 'https://apikwtdev.ourshopee.com/api/Signup' \
          -H 'accept: application/json, text/plain, */*' \
          -H 'accept-language: en-US,en;q=0.9' \
          -H 'content-type: application/json' \
          -H 'origin: https://kuwaitdev.ourshopee.com' \
          -H 'priority: u=1, i' \
          -H 'referer: https://kuwaitdev.ourshopee.com/' \
          -H 'sec-ch-ua: "Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"' \
          -H 'sec-ch-ua-mobile: ?0' \
          -H 'sec-ch-ua-platform: "macOS"' \
          -H 'sec-fetch-dest: empty' \
          -H 'sec-fetch-mode: cors' \
          -H 'sec-fetch-site: same-site' \
          -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
          --data-raw '{"first_name":"OURSHOPEE","gender":"Male","nationality":1,"email":"<EMAIL>","password":"uieewr","agreeTerms":true,"offersale":true,"mobile":"5076958485"}'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              example:
                first_name: OURSHOPEE
                gender: Male
                nationality: 1
                email: <EMAIL>
                password: "1244433445678"
                agreeTerms: true
                offersale: true
                mobile: "14662458485"
      responses:
        "200":
          description: Successful response
          content:
            application/json: {}
  /api/forgotPassword:
    post:
      tags:
        - Pre Login
      summary: forgotPassword
      requestBody:
        content:
          application/json:
            schema:
              type: object
              example:
                email: <EMAIL>
      responses:
        "200":
          description: Successful response
          content:
            application/json: {}
  /api/reset-password/{token}:
    get:
      tags:
        - Pre Login
      summary: getReset Link
      parameters:
        - name: token
          in: path
          schema:
            type: string
          required: true
          example: "{{EMAILTOKEN}}"
      responses:
        "200":
          description: Successful response
          content:
            application/json: {}
    post:
      tags:
        - Pre Login
      summary: postReset Paword link
      requestBody:
        content:
          application/json:
            schema:
              type: object
              example:
                email: <EMAIL>
                password: "123456780"
                confirm_password: "123456780"
      parameters:
        - name: token
          in: path
          schema:
            type: string
          required: true
          example: "{{EMAILTOKEN}}"
      responses:
        "200":
          description: Successful response
          content:
            application/json: {}
  /api/Login:
    post:
      tags:
        - Pre Login
      summary: Login
      description: |-
        Generated from cURL: curl 'https://api.ourshopee.com/api/Login' \
          -H 'accept: application/json, text/plain, */*' \
          -H 'accept-language: en-US,en;q=0.9' \
          -H 'content-type: application/json' \
          -H 'origin: https://ourshopee.com' \
          -H 'priority: u=1, i' \
          -H 'referer: https://ourshopee.com/' \
          -H 'sec-ch-ua: "Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"' \
          -H 'sec-ch-ua-mobile: ?0' \
          -H 'sec-ch-ua-platform: "macOS"' \
          -H 'sec-fetch-dest: empty' \
          -H 'sec-fetch-mode: cors' \
          -H 'sec-fetch-site: same-site' \
          -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
          --data-raw '{"email":"<EMAIL>","password":"12345678","oscad":""}'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              example:
                email: <EMAIL>
                password: "1234"
                oscad: ""
      responses:
        "200":
          description: Successful response
          content:
            application/json: {}
  /api/AddToCart:
    post:
      tags:
        - Cart
      summary: AddToCart
      description: |-
        Generated from cURL: curl 'https://apikwt.ourshopee.com/api/AddToCart' \
          -H 'accept: application/json, text/plain, */*' \
          -H 'accept-language: en-US,en;q=0.9' \
          -H 'authorization: Barear undefined' \
          -H 'content-type: application/json' \
          -H 'origin: https://kuwait.ourshopee.com' \
          -H 'priority: u=1, i' \
          -H 'referer: https://kuwait.ourshopee.com/' \
          -H 'sec-ch-ua: "Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"' \
          -H 'sec-ch-ua-mobile: ?0' \
          -H 'sec-ch-ua-platform: "macOS"' \
          -H 'sec-fetch-dest: empty' \
          -H 'sec-fetch-mode: cors' \
          -H 'sec-fetch-site: same-site' \
          -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
          --data-raw '{"product_id":144112,"user_id":0,"quantity":"1","country_id":5,"ip_address":0}'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              example:
                product_id: 144112
                user_id: 0
                quantity: "1"
                country_id: 5
                ip_address: 0
      responses:
        "200":
          description: Successful response
          content:
            application/json: {}
  /api/GetFromCart:
    post:
      tags:
        - Cart
      summary: GetFromCart
      description: >-
        Generated from cURL: curl 'https://apikwt.ourshopee.com/api/GetFromCart'
        \
          -H 'accept: application/json, text/plain, */*' \
          -H 'accept-language: en-US,en;q=0.9' \
          -H 'authorization: Barear undefined' \
          -H 'content-type: application/json' \
          -H 'origin: https://kuwait.ourshopee.com' \
          -H 'priority: u=1, i' \
          -H 'referer: https://kuwait.ourshopee.com/' \
          -H 'sec-ch-ua: "Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"' \
          -H 'sec-ch-ua-mobile: ?0' \
          -H 'sec-ch-ua-platform: "macOS"' \
          -H 'sec-fetch-dest: empty' \
          -H 'sec-fetch-mode: cors' \
          -H 'sec-fetch-site: same-site' \
          -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
          --data-raw '{"ip_address":"399325723"}'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              example:
                ip_address: "399325723"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: Successful response
          content:
            application/json: {}
  /api/changeCartQuantity:
    post:
      tags:
        - Cart
      summary: changeCartQuantity
      description: >-
        Generated from cURL: curl
        'https://apikwt.ourshopee.com/api/changeCartQuantity' \
          -H 'accept: application/json, text/plain, */*' \
          -H 'accept-language: en-US,en;q=0.9' \
          -H 'content-type: application/json' \
          -H 'origin: https://kuwait.ourshopee.com' \
          -H 'priority: u=1, i' \
          -H 'referer: https://kuwait.ourshopee.com/' \
          -H 'sec-ch-ua: "Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"' \
          -H 'sec-ch-ua-mobile: ?0' \
          -H 'sec-ch-ua-platform: "macOS"' \
          -H 'sec-fetch-dest: empty' \
          -H 'sec-fetch-mode: cors' \
          -H 'sec-fetch-site: same-site' \
          -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
          --data-raw '{"cart_id":687244,"quantity":4}'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              example:
                cart_id: 687244
                quantity: 4
      security:
        - bearerAuth: []
      responses:
        "200":
          description: Successful response
          content:
            application/json: {}
  /api/removeFromCart:
    post:
      tags:
        - Cart
      summary: removeFromCart
      description: >-
        Generated from cURL: curl 'https://api.ourshopee.com/api/removeFromCart'
        \
          -H 'accept: application/json, text/plain, */*' \
          -H 'accept-language: en-US,en;q=0.9' \
          -H 'content-type: application/json' \
          -H 'origin: https://ourshopee.com' \
          -H 'priority: u=1, i' \
          -H 'referer: https://ourshopee.com/' \
          -H 'sec-ch-ua: "Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"' \
          -H 'sec-ch-ua-mobile: ?0' \
          -H 'sec-ch-ua-platform: "macOS"' \
          -H 'sec-fetch-dest: empty' \
          -H 'sec-fetch-mode: cors' \
          -H 'sec-fetch-site: same-site' \
          -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
          --data-raw '{"cart_id":687256}'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              example:
                cart_id: 687256
      responses:
        "200":
          description: Successful response
          content:
            application/json: {}
  /api/category_items:
    get:
      tags:
        - Products
      summary: category_items
      description: >-
        Generated from cURL: curl 'https://api.ourshopee.com/api/category_items'
        \
          -H 'accept: application/json, text/plain, */*' \
          -H 'accept-language: en-US,en;q=0.9' \
          -H 'if-none-match: W/"c822-sBcc/YwIbWkmf3Jm4FOP5tsL/ig"' \
          -H 'origin: https://ourshopee.com' \
          -H 'priority: u=1, i' \
          -H 'referer: https://ourshopee.com/' \
          -H 'sec-ch-ua: "Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"' \
          -H 'sec-ch-ua-mobile: ?0' \
          -H 'sec-ch-ua-platform: "macOS"' \
          -H 'sec-fetch-dest: empty' \
          -H 'sec-fetch-mode: cors' \
          -H 'sec-fetch-site: same-site' \
          -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
      responses:
        "200":
          description: Successful response
          content:
            application/json: {}
  /api/getcategorylist:
    get:
      tags:
        - Category
      summary: Get Category List
      description: >-
        Generated from cURL: curl
        'https://api.ourshopee.com/api/getcategorylist' \
          -H 'accept: application/json, text/plain, */*' \
          -H 'accept-language: en-US,en;q=0.9' \
          -H 'if-none-match: W/"7e9dd-NQgymRQPIGnV5HOCn7dd761gklA"' \
          -H 'origin: https://ourshopee.com' \
          -H 'priority: u=1, i' \
          -H 'referer: https://ourshopee.com/' \
          -H 'sec-ch-ua: "Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"' \
          -H 'sec-ch-ua-mobile: ?0' \
          -H 'sec-ch-ua-platform: "macOS"' \
          -H 'sec-fetch-dest: empty' \
          -H 'sec-fetch-mode: cors' \
          -H 'sec-fetch-site: same-site' \
          -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
      responses:
        "200":
          description: Successful response
          content:
            application/json: {}
  /api/getAllBrandItems:
    get:
      tags:
        - Category
      summary: getAllBrandItems
      description: >-
        Generated from cURL: curl
        'https://api.ourshopee.com/api/getAllBrandItems?brand_id=0&slug=Dell&page=1'
        \
          -H 'accept: application/json, text/plain, */*' \
          -H 'accept-language: en-US,en;q=0.9' \
          -H 'origin: https://ourshopee.com' \
          -H 'priority: u=1, i' \
          -H 'referer: https://ourshopee.com/' \
          -H 'sec-ch-ua: "Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"' \
          -H 'sec-ch-ua-mobile: ?0' \
          -H 'sec-ch-ua-platform: "macOS"' \
          -H 'sec-fetch-dest: empty' \
          -H 'sec-fetch-mode: cors' \
          -H 'sec-fetch-site: same-site' \
          -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
      parameters:
        - name: brand_id
          in: query
          schema:
            type: integer
          example: "0"
        - name: slug
          in: query
          schema:
            type: string
          example: Apple
        - name: page
          in: query
          schema:
            type: integer
          example: "1"
      responses:
        "200":
          description: Successful response
          content:
            application/json: {}
  /api/v1/banners:
    get:
      tags:
        - Category
      summary: Get Category Banners
      responses:
        "200":
          description: Successful response
          content:
            application/json: {}
  /api/v1/subsubcategorymetatag:
    post:
      tags:
        - Category
      summary: Sub Sub category meta
      requestBody:
        content:
          application/json:
            schema:
              type: object
              example:
                url: Storage-Devices
      responses:
        "200":
          description: Successful response
          content:
            application/json: {}
  /api/v1/subcategorymetatags:
    post:
      tags:
        - Category
      summary: Sub Category Metatags
      requestBody:
        content:
          application/json:
            schema:
              type: object
              example:
                url: Storage-Devices
      responses:
        "200":
          description: Successful response
          content:
            application/json: {}
  /api/getDealOfTheDay:
    get:
      tags:
        - Home
      summary: getDealOfTheDay
      description: >-
        Generated from cURL: curl
        'https://api.ourshopee.com/api/getDealOfTheDay' \
          -H 'accept: application/json, text/plain, */*' \
          -H 'accept-language: en-US,en;q=0.9' \
          -H 'if-none-match: W/"772-bzgMmmiUaMDH1z+Xuwum8K0nYZ8"' \
          -H 'origin: https://ourshopee.com' \
          -H 'priority: u=1, i' \
          -H 'referer: https://ourshopee.com/' \
          -H 'sec-ch-ua: "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"' \
          -H 'sec-ch-ua-mobile: ?0' \
          -H 'sec-ch-ua-platform: "macOS"' \
          -H 'sec-fetch-dest: empty' \
          -H 'sec-fetch-mode: cors' \
          -H 'sec-fetch-site: same-site' \
          -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
      responses:
        "200":
          description: Successful response
          content:
            application/json: {}
  /api/getTopPicks:
    get:
      tags:
        - Home
      summary: getTopPicks
      description: |-
        Generated from cURL: curl 'https://api.ourshopee.com/api/getTopPicks' \
          -H 'accept: application/json, text/plain, */*' \
          -H 'accept-language: en-US,en;q=0.9' \
          -H 'if-none-match: W/"2cd5-VuX4biYPop3wzr2/8AHLEymhyrI"' \
          -H 'origin: https://ourshopee.com' \
          -H 'priority: u=1, i' \
          -H 'referer: https://ourshopee.com/' \
          -H 'sec-ch-ua: "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"' \
          -H 'sec-ch-ua-mobile: ?0' \
          -H 'sec-ch-ua-platform: "macOS"' \
          -H 'sec-fetch-dest: empty' \
          -H 'sec-fetch-mode: cors' \
          -H 'sec-fetch-site: same-site' \
          -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
      responses:
        "200":
          description: Successful response
          content:
            application/json: {}
  /api/deal_offers:
    get:
      tags:
        - Products
      summary: Deal Offers
      description: >-
        Generated from cURL: curl 'https://apikwt.ourshopee.com/api/deal_offers'
        \
          -H 'accept: application/json, text/plain, */*' \
          -H 'accept-language: en-US,en;q=0.9' \
          -H 'if-none-match: W/"ba7-lofO3gbrOUXynP3DCZGbmQLeyPY"' \
          -H 'origin: https://kuwait.ourshopee.com' \
          -H 'priority: u=1, i' \
          -H 'referer: https://kuwait.ourshopee.com/' \
          -H 'sec-ch-ua: "Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"' \
          -H 'sec-ch-ua-mobile: ?0' \
          -H 'sec-ch-ua-platform: "macOS"' \
          -H 'sec-fetch-dest: empty' \
          -H 'sec-fetch-mode: cors' \
          -H 'sec-fetch-site: same-site' \
          -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
      responses:
        "200":
          description: Successful response
          content:
            application/json: {}
  /api/getSaverZone:
    get:
      tags:
        - Products
      summary: getSaverZone
      description: |-
        Generated from cURL: curl 'https://api.ourshopee.com/api/getSaverZone' \
          -H 'accept: application/json, text/plain, */*' \
          -H 'accept-language: en-US,en;q=0.9' \
          -H 'origin: https://ourshopee.com' \
          -H 'priority: u=1, i' \
          -H 'referer: https://ourshopee.com/' \
          -H 'sec-ch-ua: "Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"' \
          -H 'sec-ch-ua-mobile: ?0' \
          -H 'sec-ch-ua-platform: "macOS"' \
          -H 'sec-fetch-dest: empty' \
          -H 'sec-fetch-mode: cors' \
          -H 'sec-fetch-site: same-site' \
          -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
      responses:
        "200":
          description: Successful response
          content:
            application/json: {}
  /api/getTopSelling:
    get:
      tags:
        - Home
      summary: getTopSelling
      description: >-
        Generated from cURL: curl 'https://api.ourshopee.com/api/getTopSelling'
        \
          -H 'accept: application/json, text/plain, */*' \
          -H 'accept-language: en-US,en;q=0.9' \
          -H 'if-none-match: W/"30d-fX92dbJrjDZ189DSRhh8IPOvzI8"' \
          -H 'origin: https://ourshopee.com' \
          -H 'priority: u=1, i' \
          -H 'referer: https://ourshopee.com/' \
          -H 'sec-ch-ua: "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"' \
          -H 'sec-ch-ua-mobile: ?0' \
          -H 'sec-ch-ua-platform: "macOS"' \
          -H 'sec-fetch-dest: empty' \
          -H 'sec-fetch-mode: cors' \
          -H 'sec-fetch-site: same-site' \
          -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
      responses:
        "200":
          description: Successful response
          content:
            application/json: {}
  /api/brand_week:
    get:
      tags:
        - Home
      summary: brand_week
      description: |-
        Generated from cURL: curl 'https://api.ourshopee.com/api/brand_week' \
          -H 'accept: application/json, text/plain, */*' \
          -H 'accept-language: en-US,en;q=0.9' \
          -H 'if-none-match: W/"1380-rSj4CwXu7SXXzkhpAK9bUwdaT6A"' \
          -H 'origin: https://ourshopee.com' \
          -H 'priority: u=1, i' \
          -H 'referer: https://ourshopee.com/' \
          -H 'sec-ch-ua: "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"' \
          -H 'sec-ch-ua-mobile: ?0' \
          -H 'sec-ch-ua-platform: "macOS"' \
          -H 'sec-fetch-dest: empty' \
          -H 'sec-fetch-mode: cors' \
          -H 'sec-fetch-site: same-site' \
          -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
      responses:
        "200":
          description: Successful response
          content:
            application/json: {}
  /api/bundle_clearance_sale:
    get:
      tags:
        - Home
      summary: bundle_clearance_sale
      description: >-
        Generated from cURL: curl
        'https://api.ourshopee.com/api/bundle_clearance_sale' \
          -H 'accept: application/json, text/plain, */*' \
          -H 'accept-language: en-US,en;q=0.9' \
          -H 'if-none-match: W/"8e8-pQe2gyI0RFELnweh1djfiLpgq/Y"' \
          -H 'origin: https://ourshopee.com' \
          -H 'priority: u=1, i' \
          -H 'referer: https://ourshopee.com/' \
          -H 'sec-ch-ua: "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"' \
          -H 'sec-ch-ua-mobile: ?0' \
          -H 'sec-ch-ua-platform: "macOS"' \
          -H 'sec-fetch-dest: empty' \
          -H 'sec-fetch-mode: cors' \
          -H 'sec-fetch-site: same-site' \
          -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
      responses:
        "200":
          description: Successful response
          content:
            application/json: {}
  /api/getallcategoryItems:
    get:
      tags:
        - Products
      summary: get category Items
      description: >-
        Generated from cURL: curl
        'https://api.ourshopee.com/api/getallcategoryItems?cat_url=Laptops-Computers'
        \
          -H 'accept: application/json, text/plain, */*' \
          -H 'accept-language: en-US,en;q=0.9' \
          -H 'origin: https://ourshopee.com' \
          -H 'priority: u=1, i' \
          -H 'referer: https://ourshopee.com/' \
          -H 'sec-ch-ua: "Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"' \
          -H 'sec-ch-ua-mobile: ?0' \
          -H 'sec-ch-ua-platform: "macOS"' \
          -H 'sec-fetch-dest: empty' \
          -H 'sec-fetch-mode: cors' \
          -H 'sec-fetch-site: same-site' \
          -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
      parameters:
        - name: cat_url
          in: query
          schema:
            type: string
          example: Laptops-Computers
      responses:
        "200":
          description: Successful response
          content:
            application/json: {}
  /api/clearance_sale:
    get:
      tags:
        - Products
      summary: clearance_sale
      description: >-
        Generated from cURL: curl
        'https://api.ourshopee.com/api/clearance_sale?page=1&limit=6' \
          -H 'accept: application/json, text/plain, */*' \
          -H 'accept-language: en-US,en;q=0.9' \
          -H 'if-none-match: W/"d43-8ZDDMFAz1jh52uJAW+3K4spLaZA"' \
          -H 'origin: https://ourshopee.com' \
          -H 'priority: u=1, i' \
          -H 'referer: https://ourshopee.com/' \
          -H 'sec-ch-ua: "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"' \
          -H 'sec-ch-ua-mobile: ?0' \
          -H 'sec-ch-ua-platform: "macOS"' \
          -H 'sec-fetch-dest: empty' \
          -H 'sec-fetch-mode: cors' \
          -H 'sec-fetch-site: same-site' \
          -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
      parameters:
        - name: page
          in: query
          schema:
            type: integer
          example: "1"
        - name: limit
          in: query
          schema:
            type: integer
          example: "6"
      responses:
        "200":
          description: Successful response
          content:
            application/json: {}
  /api/product_detail:
    get:
      tags:
        - Products
      summary: Get Single Product
      description: >-
        Generated from cURL: curl
        'https://api.ourshopee.com/api/product_detail?sku=PD4784' \
          -H 'accept: application/json, text/plain, */*' \
          -H 'accept-language: en-US,en;q=0.9' \
          -H 'origin: https://ourshopee.com' \
          -H 'priority: u=1, i' \
          -H 'referer: https://ourshopee.com/' \
          -H 'sec-ch-ua: "Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"' \
          -H 'sec-ch-ua-mobile: ?0' \
          -H 'sec-ch-ua-platform: "macOS"' \
          -H 'sec-fetch-dest: empty' \
          -H 'sec-fetch-mode: cors' \
          -H 'sec-fetch-site: same-site' \
          -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
      parameters:
        - name: sku
          in: query
          schema:
            type: string
          example: OC645
      responses:
        "200":
          description: Successful response
          content:
            application/json: {}
  /api/get_relatedItems:
    post:
      tags:
        - Products
      summary: Get related Items
      description: >-
        Generated from cURL: curl
        'https://api.ourshopee.com/api/get_relatedItems' \
          -H 'accept: application/json, text/plain, */*' \
          -H 'accept-language: en-US,en;q=0.9' \
          -H 'content-type: application/json' \
          -H 'origin: https://ourshopee.com' \
          -H 'priority: u=1, i' \
          -H 'referer: https://ourshopee.com/' \
          -H 'sec-ch-ua: "Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"' \
          -H 'sec-ch-ua-mobile: ?0' \
          -H 'sec-ch-ua-platform: "macOS"' \
          -H 'sec-fetch-dest: empty' \
          -H 'sec-fetch-mode: cors' \
          -H 'sec-fetch-site: same-site' \
          -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
          --data-raw '{"brand_id":1148,"subcategory_id":218,"sku":"PD4784","skulist":["OX6122","PD4784"]}'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              example:
                brand_id: 79
                subcategory_id: 337
                sku: OC645
                skulist:
                  - PJ7459
                  - PF5796
                  - OW7168
                  - OT2494
                  - PJ6855
                  - PJ6513
                  - PI9946
                  - PF8892
                  - PC1565
                  - PD5919
                  - PJ7684
                  - OC645
      responses:
        "200":
          description: Successful response
          content:
            application/json: {}
  /api/searchproducts:
    get:
      tags:
        - Products
      summary: searchproducts
      description: >-
        Generated from cURL: curl
        'https://api.ourshopee.com/api/searchproducts?str=iphone' \
          -H 'accept: application/json, text/plain, */*' \
          -H 'accept-language: en-US,en;q=0.9' \
          -H 'origin: https://ourshopee.com' \
          -H 'priority: u=1, i' \
          -H 'referer: https://ourshopee.com/' \
          -H 'sec-ch-ua: "Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"' \
          -H 'sec-ch-ua-mobile: ?0' \
          -H 'sec-ch-ua-platform: "macOS"' \
          -H 'sec-fetch-dest: empty' \
          -H 'sec-fetch-mode: cors' \
          -H 'sec-fetch-site: same-site' \
          -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
      parameters:
        - name: str
          in: query
          schema:
            type: string
          example: iphone
      responses:
        "200":
          description: Successful response
          content:
            application/json: {}
  /api/filtered_items:
    post:
      tags:
        - default
      summary: Filtered Items
      description: >-
        Generated from cURL: curl 'https://api.ourshopee.com/api/filtered_items'
        \
          -H 'accept: application/json, text/plain, */*' \
          -H 'accept-language: en-US,en;q=0.9' \
          -H 'content-type: application/json' \
          -H 'origin: https://www.ourshopee.com' \
          -H 'priority: u=1, i' \
          -H 'referer: https://www.ourshopee.com/' \
          -H 'sec-ch-ua: "Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"' \
          -H 'sec-ch-ua-mobile: ?0' \
          -H 'sec-ch-ua-platform: "macOS"' \
          -H 'sec-fetch-dest: empty' \
          -H 'sec-fetch-mode: cors' \
          -H 'sec-fetch-site: same-site' \
          -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
          --data-raw '{"page":1,"filtered_items":[{"title":"utm_source","value":["Google ads"]},{"title":"utm_medium","value":["PMAX"]},{"title":"utm_campaign","value":["F1"]},{"title":"utm_content","value":["DELL LAPTOP"]},{"title":"utm_term","value":["LAPTOP. SALE"]},{"title":"subcategory","value":[292]}]}'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              example:
                page: 1
                filtered_items:
                  - title: utm_source
                    value:
                      - Google ads
                  - title: utm_medium
                    value:
                      - PMAX
                  - title: utm_campaign
                    value:
                      - F1
                  - title: utm_term
                    value:
                      - LAPTOP. SALE
                  - title: subcategory
                    value:
                      - 292
      responses:
        "200":
          description: Successful response
          content:
            application/json: {}
  /api/saver_zone1:
    get:
      tags:
        - Products
      summary: Saver Zone
      parameters:
        - name: section_id
          in: query
          schema:
            type: integer
          example: "73"
      responses:
        "200":
          description: Successful response
          content:
            application/json: {}
  /api/get-carousel:
    get:
      tags:
        - Home
      summary: get-carousel
      responses:
        "200":
          description: Successful response
          content:
            application/json: {}
  /api/multi-banner-list:
    get:
      tags:
        - Home
      summary: multi-banner-list
      responses:
        "200":
          description: Successful response
          content:
            application/json: {}
  /api/banner-list:
    get:
      tags:
        - Home
      summary: banner-list
      responses:
        "200":
          description: Successful response
          content:
            application/json: {}
  /api/v1/get-category-list:
    post:
      tags:
        - Home
      summary: Get category list
      requestBody:
        content: {}
      responses:
        "200":
          description: Successful response
          content:
            application/json: {}
  /api/v1/brandsmetatags:
    post:
      tags:
        - Home
      summary: brandsmetatags
      requestBody:
        content:
          application/json:
            schema:
              type: object
              example:
                url: Lenovo
      responses:
        "200":
          description: Successful response
          content:
            application/json: {}
  /api/get-max-discount:
    get:
      tags:
        - Home
      summary: Get-max-discount
      responses:
        "200":
          description: Successful response
          content:
            application/json: {}
  /api/getCategorySection:
    get:
      tags:
        - Home
      summary: Get-category-section
      responses:
        "200":
          description: Successful response
          content:
            application/json: {}
  /api/GetNationality:
    get:
      tags:
        - Master
      summary: GetNationality
      responses:
        "200":
          description: Successful response
          content:
            application/json: {}
  /api/getLocations:
    get:
      tags:
        - Master
      summary: getLocations
      parameters:
        - name: id
          in: query
          schema:
            type: integer
          example: "1"
      responses:
        "200":
          description: Successful response
          content:
            application/json: {}
  /api/getAreas:
    get:
      tags:
        - Master
      summary: /getAreas
      parameters:
        - name: emirateid
          in: query
          schema:
            type: integer
          example: "2"
        - name: id
          in: query
          schema:
            type: integer
          example: "245"
      responses:
        "200":
          description: Successful response
          content:
            application/json: {}
  /api/getMyProfile:
    get:
      tags:
        - Post Login
      summary: getMyProfile
      description: |-
        Generated from cURL: curl 'https://api.ourshopee.com/api/getMyProfile' \
          -H 'accept: application/json, text/plain, */*' \
          -H 'accept-language: en-US,en;q=0.9' \
          -H 'authorization: Barear eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************.9LeQqazH3XhkUdHSCmKLXiV695l-3u1Qt6g7I9ciTKI' \
          -H 'if-none-match: W/"e0-8KZcL6LZvL5akWukWo3IUc8P9bQ"' \
          -H 'origin: https://ourshopee.com' \
          -H 'priority: u=1, i' \
          -H 'referer: https://ourshopee.com/' \
          -H 'sec-ch-ua: "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"' \
          -H 'sec-ch-ua-mobile: ?0' \
          -H 'sec-ch-ua-platform: "macOS"' \
          -H 'sec-fetch-dest: empty' \
          -H 'sec-fetch-mode: cors' \
          -H 'sec-fetch-site: same-site' \
          -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
      responses:
        "200":
          description: Successful response
          content:
            application/json: {}
  /api/changePassword:
    post:
      tags:
        - Post Login
      summary: changePassword
      description: >-
        Generated from cURL: curl 'https://api.ourshopee.com/api/changePassword'
        \
          -H 'accept: application/json, text/plain, */*' \
          -H 'accept-language: en-US,en;q=0.9' \
          -H 'authorization: Barear eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************.9LeQqazH3XhkUdHSCmKLXiV695l-3u1Qt6g7I9ciTKI' \
          -H 'content-type: application/json' \
          -H 'origin: https://ourshopee.com' \
          -H 'priority: u=1, i' \
          -H 'referer: https://ourshopee.com/' \
          -H 'sec-ch-ua: "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"' \
          -H 'sec-ch-ua-mobile: ?0' \
          -H 'sec-ch-ua-platform: "macOS"' \
          -H 'sec-fetch-dest: empty' \
          -H 'sec-fetch-mode: cors' \
          -H 'sec-fetch-site: same-site' \
          -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
          --data-raw '{"old_password":"342343","new_password":"ash0906"}'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              example:
                old_password: "342343"
                new_password: ash0906
      security:
        - bearerAuth: []
      responses:
        "200":
          description: Successful response
          content:
            application/json: {}
  /api/postWishList:
    post:
      tags:
        - Post Login
      summary: postWishList
      description: |-
        Generated from cURL: curl 'https://api.ourshopee.com/api/postWishList' \
          -H 'accept: application/json, text/plain, */*' \
          -H 'accept-language: en-US,en;q=0.9' \
          -H 'authorization: Barear eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************.L6_cRox5k9_pdiiwqt3EXVBBAdrHAhta1guOuSUrqS0' \
          -H 'content-type: application/json' \
          -H 'origin: https://ourshopee.com' \
          -H 'priority: u=1, i' \
          -H 'referer: https://ourshopee.com/' \
          -H 'sec-ch-ua: "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"' \
          -H 'sec-ch-ua-mobile: ?0' \
          -H 'sec-ch-ua-platform: "macOS"' \
          -H 'sec-fetch-dest: empty' \
          -H 'sec-fetch-mode: cors' \
          -H 'sec-fetch-site: same-site' \
          -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
          --data-raw '{"product_id":264315,"sku":"PJ7832"}'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              example:
                product_id: 264315
                sku: PJ7832
      security:
        - bearerAuth: []
      responses:
        "200":
          description: Successful response
          content:
            application/json: {}
  /api/getWishLists:
    get:
      tags:
        - Post Login
      summary: getWishLists
      description: |-
        Generated from cURL: curl 'https://api.ourshopee.com/api/getWishLists' \
          -H 'accept: application/json, text/plain, */*' \
          -H 'accept-language: en-US,en;q=0.9' \
          -H 'authorization: Barear eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************.L6_cRox5k9_pdiiwqt3EXVBBAdrHAhta1guOuSUrqS0' \
          -H 'origin: https://ourshopee.com' \
          -H 'priority: u=1, i' \
          -H 'referer: https://ourshopee.com/' \
          -H 'sec-ch-ua: "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"' \
          -H 'sec-ch-ua-mobile: ?0' \
          -H 'sec-ch-ua-platform: "macOS"' \
          -H 'sec-fetch-dest: empty' \
          -H 'sec-fetch-mode: cors' \
          -H 'sec-fetch-site: same-site' \
          -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
      security:
        - bearerAuth: []
      responses:
        "200":
          description: Successful response
          content:
            application/json: {}
  /api/updateMyProfile:
    post:
      tags:
        - Post Login
      summary: updateMyProfile
      description: >-
        Generated from cURL: curl
        'https://api.ourshopee.com/api/updateMyProfile' \
          -H 'accept: application/json, text/plain, */*' \
          -H 'accept-language: en-US,en;q=0.9' \
          -H 'authorization: Barear eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************.L6_cRox5k9_pdiiwqt3EXVBBAdrHAhta1guOuSUrqS0' \
          -H 'content-type: application/json' \
          -H 'origin: https://ourshopee.com' \
          -H 'priority: u=1, i' \
          -H 'referer: https://ourshopee.com/' \
          -H 'sec-ch-ua: "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"' \
          -H 'sec-ch-ua-mobile: ?0' \
          -H 'sec-ch-ua-platform: "macOS"' \
          -H 'sec-fetch-dest: empty' \
          -H 'sec-fetch-mode: cors' \
          -H 'sec-fetch-site: same-site' \
          -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
          --data-raw '{"first_name":"Shaikh","last_name":"Qaseem","gender":"Male","nationality":"99","email":"<EMAIL>","mobile":"508888888","agreeTerms":false,"offersale":false}'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              example:
                first_name: Shaikh
                last_name: Qaseem
                gender: Male
                nationality: "99"
                email: <EMAIL>
                mobile: "508888888"
                agreeTerms: false
                offersale: false
      security:
        - bearerAuth: []
      responses:
        "200":
          description: Successful response
          content:
            application/json: {}
  /api/myOrders:
    get:
      tags:
        - Orders
      summary: myOrders
      description: >-
        Generated from cURL: curl
        'https://api.ourshopee.com/api/myOrders?page=1&refid=' \
          -H 'accept: application/json, text/plain, */*' \
          -H 'accept-language: en-US,en;q=0.9' \
          -H 'authorization: Barear eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************.9LeQqazH3XhkUdHSCmKLXiV695l-3u1Qt6g7I9ciTKI' \
          -H 'if-none-match: W/"29d0-zUUoarKPgKY7slYODDMNKVjglVU"' \
          -H 'origin: https://ourshopee.com' \
          -H 'priority: u=1, i' \
          -H 'referer: https://ourshopee.com/' \
          -H 'sec-ch-ua: "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"' \
          -H 'sec-ch-ua-mobile: ?0' \
          -H 'sec-ch-ua-platform: "macOS"' \
          -H 'sec-fetch-dest: empty' \
          -H 'sec-fetch-mode: cors' \
          -H 'sec-fetch-site: same-site' \
          -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
      security:
        - bearerAuth: []
      parameters:
        - name: page
          in: query
          schema:
            type: integer
          example: "1"
        - name: refid
          in: query
          schema:
            type: string
      responses:
        "200":
          description: Successful response
          content:
            application/json: {}
  /api/myOrder/trackdata:
    get:
      tags:
        - Orders
      summary: Track Order
      description: >-
        Generated from cURL: curl
        'https://api.ourshopee.com/api/myOrder/trackdata?orderid=SDFJIds&contact=8373732'
        \
          -H 'accept: application/json, text/plain, */*' \
          -H 'accept-language: en-US,en;q=0.9' \
          -H 'authorization: Barear eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************.9LeQqazH3XhkUdHSCmKLXiV695l-3u1Qt6g7I9ciTKI' \
          -H 'origin: https://ourshopee.com' \
          -H 'priority: u=1, i' \
          -H 'referer: https://ourshopee.com/' \
          -H 'sec-ch-ua: "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"' \
          -H 'sec-ch-ua-mobile: ?0' \
          -H 'sec-ch-ua-platform: "macOS"' \
          -H 'sec-fetch-dest: empty' \
          -H 'sec-fetch-mode: cors' \
          -H 'sec-fetch-site: same-site' \
          -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
      security:
        - bearerAuth: []
      parameters:
        - name: orderid
          in: query
          schema:
            type: string
          example: SDFJIds
        - name: contact
          in: query
          schema:
            type: integer
          example: "8373732"
      responses:
        "200":
          description: Successful response
          content:
            application/json: {}
  /api/GetPlaceOrder:
    get:
      tags:
        - Orders
      summary: GetPlaceOrder
      description: >-
        Generated from cURL: curl
        'https://api.ourshopee.com/api/GetPlaceOrder?userId=628113' \
          -H 'accept: application/json, text/plain, */*' \
          -H 'accept-language: en-US,en;q=0.9' \
          -H 'authorization: Barear eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************.L6_cRox5k9_pdiiwqt3EXVBBAdrHAhta1guOuSUrqS0' \
          -H 'origin: https://ourshopee.com' \
          -H 'priority: u=1, i' \
          -H 'referer: https://ourshopee.com/' \
          -H 'sec-ch-ua: "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"' \
          -H 'sec-ch-ua-mobile: ?0' \
          -H 'sec-ch-ua-platform: "macOS"' \
          -H 'sec-fetch-dest: empty' \
          -H 'sec-fetch-mode: cors' \
          -H 'sec-fetch-site: same-site' \
          -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
      security:
        - bearerAuth: []
      parameters:
        - name: userId
          in: query
          schema:
            type: integer
          example: "628113"
      responses:
        "200":
          description: Successful response
          content:
            application/json: {}
  /api/getalladdresses:
    get:
      tags:
        - Addresses
      summary: getalladdresses
      responses:
        "200":
          description: Successful response
          content:
            application/json: {}
  /api/addComplaint:
    post:
      tags:
        - Complents
      summary: addComplaint
      description: |-
        Generated from cURL: curl 'https://api.ourshopee.com/api/addComplaint' \
          -H 'accept: application/json, text/plain, */*' \
          -H 'accept-language: en-US,en;q=0.9' \
          -H 'content-type: application/json' \
          -H 'origin: https://ourshopee.com' \
          -H 'priority: u=1, i' \
          -H 'referer: https://ourshopee.com/' \
          -H 'sec-ch-ua: "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"' \
          -H 'sec-ch-ua-mobile: ?0' \
          -H 'sec-ch-ua-platform: "macOS"' \
          -H 'sec-fetch-dest: empty' \
          -H 'sec-fetch-mode: cors' \
          -H 'sec-fetch-site: same-site' \
          -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
          --data-raw '{"mobile":"876676475","invoice":"SDFJIds","email":"<EMAIL>","comment":"ssdfs sa"}'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              example:
                mobile: "876676475"
                invoice: SDFJIds
                email: <EMAIL>
                comment: ssdfs sa
      security:
        - bearerAuth: []
      responses:
        "200":
          description: Successful response
          content:
            application/json: {}
  /api/getComplaint:
    post:
      tags:
        - Complents
      summary: getComplaint
      description: |-
        Generated from cURL: curl 'https://api.ourshopee.com/api/getComplaint' \
          -H 'accept: application/json, text/plain, */*' \
          -H 'accept-language: en-US,en;q=0.9' \
          -H 'authorization: Barear eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************.L6_cRox5k9_pdiiwqt3EXVBBAdrHAhta1guOuSUrqS0' \
          -H 'content-type: application/json' \
          -H 'origin: https://ourshopee.com' \
          -H 'priority: u=1, i' \
          -H 'referer: https://ourshopee.com/' \
          -H 'sec-ch-ua: "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"' \
          -H 'sec-ch-ua-mobile: ?0' \
          -H 'sec-ch-ua-platform: "macOS"' \
          -H 'sec-fetch-dest: empty' \
          -H 'sec-fetch-mode: cors' \
          -H 'sec-fetch-site: same-site' \
          -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
          --data-raw '{"cno":"78354"}'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              example:
                cno: "78354"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: Successful response
          content:
            application/json: {}
  /api/health:
    get:
      tags:
        - default
      summary: Health Checkup
      responses:
        "200":
          description: Successful response
          content:
            application/json: {}
