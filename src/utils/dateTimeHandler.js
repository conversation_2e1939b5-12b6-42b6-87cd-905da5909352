import moment from "moment";
import momentTimezone from "moment-timezone";

const TIMEZONE = process.env.TIMEZONE || "Asia/Dubai"; // Fallback if TIMEZONE is missing

export const currentDateTime = () => {
  return momentTimezone().tz(TIMEZONE).format("YYYY-MM-DD HH:mm:ss");
};

export const currentDate = () => {
  return momentTimezone().tz(TIMEZONE).format("YYYY-MM-DD");
};

export const currentTime = () => {
  return momentTimezone().tz(TIMEZONE).format("HH:mm:ss");
};

export const convertToYMD = (date = "") => {
  return date.split("-").reverse().join("-"); // e.g. 31-12-2023 → 2023-12-31
};

export const convertToDMY = (date = "") => {
  return moment(date).utc().format("DD-MM-YYYY");
};

export const convertToDMYTime = (date = "") => {
  return moment(date).utc().format("DD-MM-YYYY HH:mm:ss");
};
