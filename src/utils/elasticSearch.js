export const getProductsByIdsELK = async (req, fastify) => {
    const {
        section_id = 0,
        product_id = 0,
        front_view = 0,
        condition_quantity = false,
        limit = 10,
        offset = 0,
    } = req;

    const client = fastify.elasticsearch;

    const mustArray = [{ match: { status: 1 } }];

    if (condition_quantity) {
        mustArray.push(
            { range: { special_price: { gte: 1 } } },
            { range: { quantity: { gte: 1 } } },
            { match: { stock: "In stock" } }
        );
    }

    if (section_id > 0) mustArray.push({ match: { section_id } });
    if (product_id > 0) mustArray.push({ match: { product_id } });
    if (front_view > 0) mustArray.push({ match: { front_view: 1 } });

    const sortOrder = condition_quantity
        ? [{ id: { order: "desc" } }]
        : [
              { from_date: { order: "desc" } },
              { updated_date: { order: "desc" } },
          ];

    try {
        const result = await client.search({
            index: process.env.ELASTIC_INDEX,
            body: {
                from: parseInt(offset, 10),
                size: parseInt(limit, 10),
                sort: sortOrder,
                query: {
                    bool: {
                        must: mustArray,
                    },
                },
            },
        });

        return result.hits?.hits?.map((hit) => hit._source) || [];
    } catch (error) {
        fastify.log.error(
            " Elasticsearch error in getProductsByIdsELK:",
            error
        );
        return [];
    }
};
