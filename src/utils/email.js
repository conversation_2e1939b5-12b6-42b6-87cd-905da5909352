import nodemailer from "nodemailer";

import getCustomResetPasswordHtml from "./getCustomResetPasswordHtml.js";

const sendEmailUsingMailGen = async (options) => {
    let htmlContent = options?.username
        ? getCustomResetPasswordHtml(options.username, options.resetlink)
        : options.htmlContent;

    const transporter = nodemailer.createTransport({
        host: process.env.NODE_MAILER_HOST,
        port: process.env.NODE_MAILER_PORT,
        secure: process.env.NODE_MAILER_TLS_SECURE === "true", // TLS uses secure: false
        auth: {
            user: process.env.NODE_MAILER_EMAIL,
            pass: process.env.NODE_MAILER_PASS,
        },
        tls: {
            rejectUnauthorized: false, // optional: allow self-signed certs
        },
    });

    const mail = {
        from: process.env.NODE_MAILER_EMAIL, // We can name this anything. The mail will go to your Mailtrap inbox
        to: options.email, // receiver's mail
        subject: options.subject, // mail subject
        html: htmlContent, // mailgen content html variant
        text: htmlContent, // mailgen content textual variant
    };

    try {
        await transporter.sendMail(mail);
    } catch (error) {
        // As sending email is not strongly coupled to the business logic it is not worth to raise an error when email sending fails
        // So it's better to fail silently rather than breaking the app
        console.error(
            "Email service failed silently. Make sure you have provided your MAILTRAP credentials in the .env file"
        );
        console.error("Error: ", error);
    }
};

export { sendEmailUsingMailGen };
