const getCustomResetPasswordHtml = (username, passwordResetUrl) => {
    return `
    <!DOCTYPE html>
    <html xmlns="http://www.w3.org/1999/xhtml">
      <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <title>Password Reset</title>
        <style type="text/css">
          @media screen and (max-width:1920px) {
            .main-table { padding-left:20px!important; padding-right:20px!important; }
          }
          @media screen and (max-width:767px) {
            .img-max { max-width: 100%!important; width: 100%!important; height: auto!important; }
            .main-table { padding-left:15px!important; padding-right:15px!important; }
          }
        </style>
      </head>
      <body>
        <table align="center" cellpadding="0" cellspacing="0" border="0">
          <tr><td>
            <div style="min-width:800px;margin:0 auto;border:1px solid #D9DDEA;">
              <table width="800" cellpadding="0" cellspacing="0" style="background:#fff;box-shadow:0 3px 0 rgba(0,0,0,0.2);border-radius:6px;padding:10px 25px;" class="main-table">
                <tr>
                  <td align="right">
                    <a href="https://www.ourshopee.com/" target="_blank">
                      <img src="https://cdn.ourshopee.com/img/logo.png" width="110" style="width:110px;">
                    </a>
                  </td>
                </tr>
                <tr>
                  <td style="font-size:20px;font-weight:bold;font-family:Arial;color:#45434a;">
                    Reset your password.
                  </td>
                </tr>
                <tr>
                  <td align="center">
                    <img src="https://cdn.ourshopee.com/images/mails/reset.jpg" style="width:85%;">
                  </td>
                </tr>
                <tr>
                  <td>
                    <p style="font-family:Arial;color:#000;font-size:14px;font-weight:600;">Hello ${username},</p>
                    <p style="font-family:Arial;color:#000;font-size:13px;">We got a request to reset the password of your account.</p>
                    <p style="font-family:Arial;color:#000;font-size:13px;">
                      To confirm this action <a href="${passwordResetUrl}" target="_blank" style="text-decoration:underline;">Click Here</a>, or follow the link below:
                    </p>
                    <p style="font-family:Arial;color:#0153a9;font-size:11px;font-weight:600;">${passwordResetUrl}</p>
                    <p style="font-family:Arial;color:#000;font-size:13px;">You can simply ignore this message if you did not request the password reset or just do not want to reset it now.</p>
                    <br />
                    <p style="font-family:Arial;color:#000;font-size:12px;font-weight:500;">Thank You,</p>
                    <p style="font-family:Arial;color:#000;font-size:13px;font-weight:600;">Team OurShopee</p>
                  </td>
                </tr>
                <tr>
                  <td align="center" style="padding:15px 0;">
                    <p style="font-family:Arial;font-size:12px;font-weight:bold;color:#000;">CONNECT WITH US</p>
                    <a href="https://www.facebook.com/ourshopee" target="_blank"><img src="https://cdn.ourshopee.com/images/fb.png"></a>
                    <a href="https://plus.google.com/+OURSHOPEEcom/posts" target="_blank"><img src="https://cdn.ourshopee.com/images/google.png"></a>
                    <a href="https://twitter.com/ourshopee" target="_blank"><img src="https://cdn.ourshopee.com/images/twitter.png"></a>
                    <a href="https://www.linkedin.com/company/ourshopee-com" target="_blank"><img src="https://cdn.ourshopee.com/images/linked.png"></a>
                    <p style="font-family:Arial;font-size:12px;color:#666;">
                      Email: <a href="mailto:<EMAIL>" style="color:#000"><EMAIL></a><br />
                      Policy and Agreement | <a href="https://www.ourshopee.com/privacy-policy/" style="color:#333;">Privacy Policy</a><br />
                      &copy; ${new Date().getFullYear()} OurShopee.com. All rights reserved.
                    </p>
                  </td>
                </tr>
              </table>
            </div>
          </td></tr>
        </table>
      </body>
    </html>`;
};

export default getCustomResetPasswordHtml;
