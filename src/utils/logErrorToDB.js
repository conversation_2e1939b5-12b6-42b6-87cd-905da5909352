import OurshopeeErrorLog from "../models/ourshopee_error_log.model.js";
import { currentDateTime } from "./dateTimeHandler.js";
export const logErrorToDB = async (req, err) => {
    try {
        const route = req.routerPath || req.url || "N/A";
        const method = req.method || "N/A";
        const payload = req.body || null;

        await OurshopeeErrorLog.create({
            user_id: req?.user?.user_id || null,
            route: route,
            method: method,
            message: err?.message || String(err),
            stack: err?.stack || null,
            payload: payload,
            created_at: currentDateTime(),
        });
    } catch (err) {
        console.error("Failed to log error to DB: ", err.message);
    }
};
