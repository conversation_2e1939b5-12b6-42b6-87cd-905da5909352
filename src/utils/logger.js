import morgan from "morgan";
import { createStream } from "rotating-file-stream";
import path from "path";
import fs from "fs";
import { fileURLToPath } from "url";
import { COUNTRY_NAMES } from "../region/config.js";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create logs directory structure if it doesn't exist
const logsDir = path.join(__dirname, "../../logs");
const accessLogsDir = path.join(logsDir, "access");
const errorLogsDir = path.join(logsDir, "error");

if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir, { recursive: true });
}
if (!fs.existsSync(accessLogsDir)) {
    fs.mkdirSync(accessLogsDir, { recursive: true });
}
if (!fs.existsSync(errorLogsDir)) {
    fs.mkdirSync(errorLogsDir, { recursive: true });
}

// Function to generate log file names (used by rotating-file-stream)
function generateLogFileName(countryName, type) {
    return (time, index) => {
        if (!time) {
            // For the current file, use today's date
            const date = new Date();
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, "0");
            const day = String(date.getDate()).padStart(2, "0");
            return `shopee-${countryName}-${type}-${year}-${month}-${day}.log`;
        }

        // For rotated files, format the time properly
        const date = new Date(time);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");

        if (index) {
            return `shopee-${countryName}-${type}-${year}-${month}-${day}-${index}.log.gz`;
        }
        return `shopee-${countryName}-${type}-${year}-${month}-${day}.log.gz`;
    };
}

// Create streams for each country
const accessStreams = {};
const errorStreams = {};

// Initialize streams for all countries
Object.entries(COUNTRY_NAMES).forEach(([countryId, countryName]) => {
    // Create access log stream for each country
    accessStreams[countryId] = createStream(
        generateLogFileName(countryName, "access"),
        {
            interval: "1d", // rotate daily
            path: accessLogsDir,
            maxFiles: 10, // keep 10 days of logs
            compress: "gzip", // compress rotated files
            size: "10M", // rotate when file size reaches 10MB
        }
    );

    // Create error log stream for each country
    errorStreams[countryId] = createStream(
        generateLogFileName(countryName, "error"),
        {
            interval: "1d", // rotate daily
            path: errorLogsDir,
            maxFiles: 10, // keep 10 days of logs
            compress: "gzip", // compress rotated files
            size: "10M", // rotate when file size reaches 10MB
        }
    );
});

// Custom Morgan format with country info
const logFormat =
    ":date[iso] :method :url :status :response-time-custom ms - :res[content-length] - :remote-addr - :user-agent - Country: :country-id";

// Custom Morgan format for errors (includes error body and request body)
const errorLogFormat =
    ":date[iso] :method :url :status :response-time-custom ms - :res[content-length] - :remote-addr - :user-agent - Country: :country-id - RequestBody: :request-body - Error: :error-body";

// Add custom token for country-id
morgan.token("country-id", (req) => {
    return req.headers["country-id"] || "unknown";
});

// Add custom token for request body
morgan.token("request-body", (req) => {
    try {
        // Try to get the request body from various sources
        let body = req.requestBody || req.body || req.rawBody || req.payload;

        // If no body found, try to get it from the original request
        if (!body && req.raw && req.raw.requestBody) {
            body = req.raw.requestBody;
        }

        if (body && typeof body === "object") {
            // Limit the size to prevent huge logs
            const bodyStr = JSON.stringify(body);
            return bodyStr.length > 1000
                ? bodyStr.substring(0, 1000) + "...[truncated]"
                : bodyStr;
        } else if (body && typeof body === "string") {
            return body.length > 1000
                ? body.substring(0, 1000) + "...[truncated]"
                : body;
        }

        return "no-body";
    } catch (error) {
        return "body-parse-error";
    }
});

morgan.token("body", (req) => {
    return JSON.stringify(req.body || {});
});

// Add custom token for error body
morgan.token("error-body", (req, res) => {
    if (res.statusCode >= 400) {
        // Try to get error message from various sources
        return (
            res.locals?.errorMessage ||
            res.errorMessage ||
            res.statusMessage ||
            `HTTP ${res.statusCode} Error`
        );
    }
    return "";
});

// Add custom token for response time (Fastify compatible)
morgan.token("response-time-custom", (req, res) => {
    if (req.startTime) {
        return (Date.now() - req.startTime).toString();
    }
    return "-";
});

// Middleware factory function
export function createCountryLogger() {
    return [
        // Console logging
        morgan(logFormat),

        // Country-specific access logging
        morgan(logFormat, {
            stream: {
                write: (message) => {
                    // Extract country-id from the log message or use a fallback
                    const countryMatch = message.match(
                        /Country: (\d+|unknown)/
                    );
                    const countryId = countryMatch ? countryMatch[1] : "1"; // default to UAE

                    if (countryId === "unknown") {
                        // Log unknown country requests to UAE logs
                        accessStreams["1"].write(message);
                    } else if (accessStreams[countryId]) {
                        accessStreams[countryId].write(message);
                    } else {
                        // Fallback to UAE logs if country not found
                        accessStreams["1"].write(message);
                    }
                },
            },
            skip: function (req, res) {
                return res.statusCode >= 400;
            }, // Only log successful requests to access log
        }),

        // Country-specific error logging with error details
        morgan(errorLogFormat, {
            stream: {
                write: (message) => {
                    // Extract country-id from the log message or use a fallback
                    const countryMatch = message.match(
                        /Country: (\d+|unknown)/
                    );
                    const countryId = countryMatch ? countryMatch[1] : "1"; // default to UAE

                    if (countryId === "unknown") {
                        // Log unknown country requests to UAE logs
                        errorStreams["1"].write(message);
                    } else if (errorStreams[countryId]) {
                        errorStreams[countryId].write(message);
                    } else {
                        // Fallback to UAE logs if country not found
                        errorStreams["1"].write(message);
                    }
                },
            },
            skip: function (req, res) {
                return res.statusCode < 400;
            }, // Only log errors to error log
        }),
    ];
}

export { accessStreams, errorStreams, COUNTRY_NAMES };
