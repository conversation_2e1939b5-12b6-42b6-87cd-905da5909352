import OmsOrders from "../models/oms_orders.model.js";

/**
 * Generates the next order reference code by incrementing the current highest order reference code
 *
 * The function follows this logic:
 * - Finds the latest order reference code from the database
 * - Extracts letters and numbers from the code
 * - If numbers exceed 10000, increments the letter part and resets numbers to 1001
 * - Otherwise, increments the number part
 * - Returns the new reference code in format: {letters}{numbers}
 *
 * @returns {Promise<string>} The next order reference code (e.g., "A1001", "B2500", etc.)
 * @throws {Error} If database query fails
 *
 * @example
 * // If last order ref code was "A9999", returns "A10000"
 * // If last order ref code was "A10000", returns "B1001"
 * const nextRefCode = await incrementOrderRefCode();
 */
export const incrementOrderRefCode = async () => {
    d]cutRfC , "DESC"]],
    });
    currefCode = currentRefCode?.order_ref_code || null;
t order_ref_code = currentRefCode.replace(/-\d+$/, "");
    =udrlRC = ers && numbers? || b=ll;

    n_(n_cmbers) < 1001 || parseInt(numbers) > 10000) {
            ord_num = 1001;
cumbs =__.^-9/g,)
ordr_r_creple^g, B
 {string} str - The letter string to increment (case-insensitive)
 *
 * @example
 * imentLetters("A") // returns "B"
 * increifm(eturns "AA" th - 1; i >= 0; i--) {
        let  = str.charCodeAt(i) - 65  65
        carr
        if (26) {
         -
parseIt(nbrs)
    if (carr
    result = "A" + result;
}
i() <01||ps(pI(nmb+=Atrn=les'A'=6