import OmsOrders from "../models/oms_orders.model.js";

/**
 * Order utilities for managing order reference codes and related functionality
 *
 * Usage example:
 * ```javascript
 * import { incrementOrderRefCode } from "../utils/orderUtils.js";
 *
 * // Generate next order reference code
 * const refCode = await incrementOrderRefCode();
 * console.log(refCode); // e.g., "A1001", "B2500", etc.
 * ```
 */

/**
 * Generates the next order reference code by incrementing the current highest order reference code
 *
 * The function follows this logic:
 * - Finds the latest order reference code from the database
 * - Extracts letters and numbers from the code
 * - If numbers exceed 10000, increments the letter part and resets numbers to 1001
 * - Otherwise, increments the number part
 * - Returns the new reference code in format: {letters}{numbers}
 *
 * @returns {Promise<string>} The next order reference code (e.g., "A1001", "B2500", etc.)
 * @throws {Error} If database query fails
 *
 * @example
 * // If last order ref code was "A9999", returns "A10000"
 * // If last order ref code was "A10000", returns "B1001"
 * const nextRefCode = await incrementOrderRefCode();
 */
export const incrementOrderRefCode = async () => {
    let currentRefCode = await OmsOrders.findOne({
        attributes: ["order_ref_code"],
        order: [["orderid", "DESC"]],
    });
    currentRefCode = currentRefCode?.order_ref_code || null;

    const order_ref_code = currentRefCode.replace(/-\d+$/, "");

    const numbers = order_ref_code.replace(/[^0-9]/g, "");
    const letters = order_ref_code.replace(/[^a-zA-Z]/g, "");

    let ord_num, ord_alp;

    if (letters && numbers) {
        if (parseInt(numbers) > 10000) {
            // Increment alphabet part
            ord_alp = incrementLetters(letters);
            ord_num = 1001;
        } else {
            ord_num = parseInt(numbers) + 1;
            ord_alp = letters;
        }
    } else {
        if (parseInt(numbers) < 1001 || parseInt(numbers) > 10000) {
            ord_num = 1001;
        } else {
            ord_num = parseInt(numbers) + 1;
        }
        ord_alp = "A";
    }

    return `${ord_alp}${ord_num}`;
};

/**
 * Helper function to increment letter part like A → B, Z → AA, etc.
 *
 * This function works like a base-26 counter where:
 * - A = 0, B = 1, ..., Z = 25
 * - When Z is reached, it carries over: Z → AA, AZ → BA, etc.
 *
 * @param {string} str - The letter string to increment (case-insensitive)
 * @returns {string} The incremented letter string in uppercase
 *
 * @example
 * incrementLetters("A") // returns "B"
 * incrementLetters("Z") // returns "AA"
 * incrementLetters("AZ") // returns "BA"
 * incrementLetters("ZZ") // returns "AAA"
 */
export const incrementLetters = (str) => {
    str = str.toUpperCase();
    let carry = 1;
    let result = "";

    for (let i = str.length - 1; i >= 0; i--) {
        let code = str.charCodeAt(i) - 65 + carry; // 'A' = 65
        carry = 0;

        if (code >= 26) {
            code -= 26;
            carry = 1;
        }

        result = String.fromCharCode(65 + code) + result;
    }

    if (carry) {
        result = "A" + result;
    }

    return result;
};
