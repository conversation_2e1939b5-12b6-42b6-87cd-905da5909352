/**
 * Test file for orderUtils functions
 * 
 * To run these tests, you can use a testing framework like Je<PERSON> or Mocha
 * Example usage with Node.js (without a testing framework):
 * node src/utils/orderUtils.test.js
 */

import { incrementLetters } from './orderUtils.js';

// Test the incrementLetters function
function testIncrementLetters() {
    console.log('Testing incrementLetters function...');
    
    const testCases = [
        { input: 'A', expected: 'B' },
        { input: 'B', expected: 'C' },
        { input: 'Z', expected: 'AA' },
        { input: 'AA', expected: 'AB' },
        { input: 'AZ', expected: 'BA' },
        { input: 'ZZ', expected: 'AAA' },
        { input: 'a', expected: 'B' }, // Test case insensitive
        { input: 'z', expected: 'AA' }, // Test case insensitive
    ];
    
    let passed = 0;
    let failed = 0;
    
    testCases.forEach(({ input, expected }) => {
        const result = incrementLetters(input);
        if (result === expected) {
            console.log(`✅ PASS: incrementLetters('${input}') = '${result}'`);
            passed++;
        } else {
            console.log(`❌ FAIL: incrementLetters('${input}') = '${result}', expected '${expected}'`);
            failed++;
        }
    });
    
    console.log(`\nTest Results: ${passed} passed, ${failed} failed\n`);
    return failed === 0;
}

// Example usage demonstration
function demonstrateUsage() {
    console.log('=== Order Utils Usage Examples ===');
    console.log('');
    console.log('// Import the function');
    console.log('import { incrementOrderRefCode } from "../utils/orderUtils.js";');
    console.log('');
    console.log('// Use in your service');
    console.log('const refCode = await incrementOrderRefCode();');
    console.log('console.log(refCode); // e.g., "A1001", "B2500", etc.');
    console.log('');
    console.log('// Example sequence:');
    console.log('// If last order was "A9999", next will be "A10000"');
    console.log('// If last order was "A10000", next will be "B1001"');
    console.log('// If last order was "Z10000", next will be "AA1001"');
    console.log('');
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    demonstrateUsage();
    testIncrementLetters();
}

export { testIncrementLetters };
