import { logErrorToDB } from "./logErrorToDB.js";
function successResponse(
    reply,
    statusCode = 200,
    msg,
    status = "success",
    data = []
) {
    return reply.code(statusCode).send({
        message: msg || "Something went wrong! While processing your request",
        status: status,
        data: data,
    });
}

function errorResponse(
    reply,
    statusCode = 500,
    error = "Something went wrong!",
    data = {},
    status = "failure",
    req = {}
) {
    logErrorToDB(req, error);

    const NODE_ENV = process.env.NODE_ENV;
    return reply.status(statusCode).send({
        status,
        message:
            NODE_ENV === "development"
                ? error?.message ||
                  "Something went wrong while processing your request"
                : "Something went wrong while processing your request",
        data,
    });
}

export { successResponse, errorResponse };
